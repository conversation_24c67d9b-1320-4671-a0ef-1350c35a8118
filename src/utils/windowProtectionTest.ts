/**
 * 窗口保护机制测试工具
 * 用于测试和验证窗口保护功能
 */

import { WindowProtectionManager } from './windowProtectionManager';

/**
 * 窗口保护机制测试类
 */
export class WindowProtectionTest {
  
  /**
   * 测试窗口保护需求检查
   */
  static async testProtectionNeeds(): Promise<void> {
    console.log('🧪 [WindowProtectionTest] 开始测试窗口保护需求检查...');
    
    try {
      const result = await WindowProtectionManager.shouldProtectWindow();
      
      if (result.success) {
        const { needsProtection, reason, tabCounts } = result.data!;
        
        console.log('📊 [WindowProtectionTest] 窗口保护检查结果:');
        console.log(`  需要保护: ${needsProtection ? '是' : '否'}`);
        console.log(`  原因: ${reason}`);
        console.log('  标签页统计:', tabCounts);
        
        if (needsProtection) {
          console.log('⚠️ [WindowProtectionTest] 窗口需要保护！');
        } else {
          console.log('✅ [WindowProtectionTest] 窗口安全，无需保护');
        }
      } else {
        console.error('❌ [WindowProtectionTest] 窗口保护检查失败:', result.error);
      }
    } catch (error) {
      console.error('❌ [WindowProtectionTest] 测试过程中出错:', error);
    }
  }
  
  /**
   * 测试保护标签页创建
   */
  static async testProtectionTabCreation(): Promise<void> {
    console.log('🧪 [WindowProtectionTest] 开始测试保护标签页创建...');
    
    try {
      const result = await WindowProtectionManager.createProtectionTab();
      
      if (result.success) {
        const protectionTabId = result.data!;
        console.log(`✅ [WindowProtectionTest] 成功创建保护标签页: ${protectionTabId}`);
        
        // 验证标签页是否真的被创建
        try {
          const tab = await chrome.tabs.get(protectionTabId);
          console.log(`🔍 [WindowProtectionTest] 保护标签页验证: ${tab.title} (${tab.url})`);
        } catch (verifyError) {
          console.error('❌ [WindowProtectionTest] 保护标签页验证失败:', verifyError);
        }
      } else {
        console.error('❌ [WindowProtectionTest] 保护标签页创建失败:', result.error);
      }
    } catch (error) {
      console.error('❌ [WindowProtectionTest] 测试过程中出错:', error);
    }
  }
  
  /**
   * 测试智能窗口保护
   */
  static async testSmartProtection(): Promise<void> {
    console.log('🧪 [WindowProtectionTest] 开始测试智能窗口保护...');
    
    try {
      const result = await WindowProtectionManager.protectWindowIfNeeded();
      
      if (result.success) {
        const { protected: isProtected, protectionTabId, reason } = result.data!;
        
        console.log('📊 [WindowProtectionTest] 智能窗口保护结果:');
        console.log(`  已保护: ${isProtected ? '是' : '否'}`);
        console.log(`  原因: ${reason}`);
        
        if (isProtected && protectionTabId) {
          console.log(`  保护标签页ID: ${protectionTabId}`);
          
          // 验证保护标签页
          try {
            const tab = await chrome.tabs.get(protectionTabId);
            console.log(`🔍 [WindowProtectionTest] 保护标签页详情: ${tab.title} (${tab.url})`);
          } catch (verifyError) {
            console.error('❌ [WindowProtectionTest] 保护标签页验证失败:', verifyError);
          }
        }
      } else {
        console.error('❌ [WindowProtectionTest] 智能窗口保护失败:', result.error);
      }
    } catch (error) {
      console.error('❌ [WindowProtectionTest] 测试过程中出错:', error);
    }
  }
  
  /**
   * 运行所有测试
   */
  static async runAllTests(): Promise<void> {
    console.log('🚀 [WindowProtectionTest] 开始运行所有窗口保护测试...');
    
    await this.testProtectionNeeds();
    console.log('---');
    
    await this.testSmartProtection();
    console.log('---');
    
    // 注意：保护标签页创建测试可能会实际创建标签页，谨慎使用
    // await this.testProtectionTabCreation();
    
    console.log('✅ [WindowProtectionTest] 所有测试完成');
  }
  
  /**
   * 模拟危险场景测试
   * 注意：这个测试可能会实际影响浏览器状态，仅用于开发测试
   */
  static async simulateDangerousScenario(): Promise<void> {
    console.log('⚠️ [WindowProtectionTest] 模拟危险场景测试（仅开发环境）...');
    
    try {
      // 获取当前窗口的所有标签页
      const allTabs = await chrome.tabs.query({ currentWindow: true });
      console.log(`📊 [WindowProtectionTest] 当前窗口有 ${allTabs.length} 个标签页`);
      
      // 分析标签页类型
      let userTabs = 0;
      let systemTabs = 0;
      
      for (const tab of allTabs) {
        if (tab.url && (
          tab.url.includes('chrome://') ||
          tab.url.includes('chrome-extension://') ||
          tab.url.includes('about:')
        )) {
          systemTabs++;
        } else {
          userTabs++;
        }
      }
      
      console.log(`📊 [WindowProtectionTest] 标签页分析: 用户标签页 ${userTabs} 个，系统标签页 ${systemTabs} 个`);
      
      // 如果用户标签页数量 <= 1，这是一个危险场景
      if (userTabs <= 1) {
        console.log('🚨 [WindowProtectionTest] 检测到危险场景：用户标签页数量 <= 1');
        
        // 测试保护机制
        const protectionResult = await WindowProtectionManager.protectWindowIfNeeded();
        
        if (protectionResult.success && protectionResult.data!.protected) {
          console.log(`✅ [WindowProtectionTest] 保护机制已激活: ${protectionResult.data!.reason}`);
        } else {
          console.log(`ℹ️ [WindowProtectionTest] 保护机制未激活: ${protectionResult.data!.reason}`);
        }
      } else {
        console.log('✅ [WindowProtectionTest] 当前场景安全，无需保护');
      }
    } catch (error) {
      console.error('❌ [WindowProtectionTest] 危险场景测试失败:', error);
    }
  }
}

// 导出测试函数供控制台调用
(window as any).WindowProtectionTest = WindowProtectionTest;

console.log('🧪 [WindowProtectionTest] 测试工具已加载，可在控制台使用：');
console.log('  WindowProtectionTest.runAllTests() - 运行所有测试');
console.log('  WindowProtectionTest.testProtectionNeeds() - 测试保护需求检查');
console.log('  WindowProtectionTest.testSmartProtection() - 测试智能保护');
console.log('  WindowProtectionTest.simulateDangerousScenario() - 模拟危险场景');
