/**
 * 性能测试工具
 * 用于验证标签页管理功能的性能优化效果
 */

import { TabInfo } from '@/types/workspace';
import { createTabStateSnapshot, compareTabSnapshots } from './tabStateComparator';

interface PerformanceMetrics {
  renderCount: number;
  lastRenderTime: number;
  averageRenderTime: number;
  totalRenderTime: number;
  memoryUsage: number;
  componentUpdateCount: number;
}

/**
 * 性能测试管理器
 */
export class PerformanceTestManager {
  private static instance: PerformanceTestManager | null = null;
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private renderTimers: Map<string, number> = new Map();
  private isMonitoring = false;

  private constructor() {}

  static getInstance(): PerformanceTestManager {
    if (!PerformanceTestManager.instance) {
      PerformanceTestManager.instance = new PerformanceTestManager();
    }
    return PerformanceTestManager.instance;
  }

  /**
   * 开始性能监控
   */
  startMonitoring(): void {
    if (this.isMonitoring) return;

    console.log('📊 [PerformanceTest] 开始性能监控');
    this.isMonitoring = true;
    this.metrics.clear();
    this.renderTimers.clear();

    // 监控内存使用情况
    this.startMemoryMonitoring();
  }

  /**
   * 停止性能监控
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) return;

    console.log('📊 [PerformanceTest] 停止性能监控');
    this.isMonitoring = false;
  }

  /**
   * 记录组件渲染开始
   */
  recordRenderStart(componentName: string): void {
    if (!this.isMonitoring) return;

    const startTime = performance.now();
    this.renderTimers.set(componentName, startTime);
  }

  /**
   * 记录组件渲染结束
   */
  recordRenderEnd(componentName: string): void {
    if (!this.isMonitoring) return;

    const startTime = this.renderTimers.get(componentName);
    if (!startTime) return;

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    this.renderTimers.delete(componentName);

    // 更新性能指标
    const currentMetrics = this.metrics.get(componentName) || {
      renderCount: 0,
      lastRenderTime: 0,
      averageRenderTime: 0,
      totalRenderTime: 0,
      memoryUsage: 0,
      componentUpdateCount: 0,
    };

    currentMetrics.renderCount++;
    currentMetrics.lastRenderTime = renderTime;
    currentMetrics.totalRenderTime += renderTime;
    currentMetrics.averageRenderTime = currentMetrics.totalRenderTime / currentMetrics.renderCount;
    currentMetrics.memoryUsage = this.getCurrentMemoryUsage();

    this.metrics.set(componentName, currentMetrics);

    console.log(`⏱️ [PerformanceTest] ${componentName} 渲染时间: ${renderTime.toFixed(2)}ms`);
  }

  /**
   * 记录组件更新
   */
  recordComponentUpdate(componentName: string): void {
    if (!this.isMonitoring) return;

    const currentMetrics = this.metrics.get(componentName);
    if (currentMetrics) {
      currentMetrics.componentUpdateCount++;
      this.metrics.set(componentName, currentMetrics);
    }
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): {
    summary: string;
    details: Record<string, PerformanceMetrics>;
    recommendations: string[];
  } {
    const details: Record<string, PerformanceMetrics> = {};
    let totalRenders = 0;
    let totalRenderTime = 0;
    let slowestComponent = '';
    let slowestTime = 0;

    for (const [componentName, metrics] of this.metrics) {
      details[componentName] = { ...metrics };
      totalRenders += metrics.renderCount;
      totalRenderTime += metrics.totalRenderTime;

      if (metrics.averageRenderTime > slowestTime) {
        slowestTime = metrics.averageRenderTime;
        slowestComponent = componentName;
      }
    }

    const averageRenderTime = totalRenders > 0 ? totalRenderTime / totalRenders : 0;

    const summary = `
性能测试报告:
- 总渲染次数: ${totalRenders}
- 总渲染时间: ${totalRenderTime.toFixed(2)}ms
- 平均渲染时间: ${averageRenderTime.toFixed(2)}ms
- 最慢组件: ${slowestComponent} (${slowestTime.toFixed(2)}ms)
- 当前内存使用: ${this.getCurrentMemoryUsage().toFixed(2)}MB
    `.trim();

    const recommendations: string[] = [];

    // 生成优化建议
    for (const [componentName, metrics] of this.metrics) {
      if (metrics.averageRenderTime > 10) {
        recommendations.push(`${componentName}: 平均渲染时间过长 (${metrics.averageRenderTime.toFixed(2)}ms)，建议优化`);
      }
      if (metrics.renderCount > 50) {
        recommendations.push(`${componentName}: 渲染次数过多 (${metrics.renderCount}次)，可能存在不必要的重新渲染`);
      }
    }

    if (recommendations.length === 0) {
      recommendations.push('性能表现良好，无需优化');
    }

    return {
      summary,
      details,
      recommendations,
    };
  }

  /**
   * 测试状态比较性能
   */
  async testStateComparisonPerformance(
    oldTabs: TabInfo[],
    newTabs: TabInfo[],
    iterations: number = 1000
  ): Promise<{
    averageTime: number;
    totalTime: number;
    iterations: number;
  }> {
    console.log(`🧪 [PerformanceTest] 开始状态比较性能测试 (${iterations} 次迭代)`);

    const startTime = performance.now();

    for (let i = 0; i < iterations; i++) {
      const oldSnapshot = createTabStateSnapshot(oldTabs);
      const newSnapshot = createTabStateSnapshot(newTabs);
      compareTabSnapshots(oldSnapshot, newSnapshot);
    }

    const endTime = performance.now();
    const totalTime = endTime - startTime;
    const averageTime = totalTime / iterations;

    console.log(`✅ [PerformanceTest] 状态比较性能测试完成:`);
    console.log(`  - 总时间: ${totalTime.toFixed(2)}ms`);
    console.log(`  - 平均时间: ${averageTime.toFixed(4)}ms`);
    console.log(`  - 迭代次数: ${iterations}`);

    return {
      averageTime,
      totalTime,
      iterations,
    };
  }

  /**
   * 模拟大量标签页测试
   */
  generateMockTabs(count: number): TabInfo[] {
    const mockTabs: TabInfo[] = [];

    for (let i = 0; i < count; i++) {
      mockTabs.push({
        id: i + 1,
        chromeTabId: i + 1,
        url: `https://example${i}.com`,
        title: `标签页 ${i + 1}`,
        favicon: `https://example${i}.com/favicon.ico`,
        isOpen: Math.random() > 0.3,
        isActive: i === 0,
        isPinned: Math.random() > 0.8,
        isWorkspaceCore: Math.random() > 0.5,
        windowId: 1,
        index: i,
        workonaId: `t-workspace1-${i}`,
        lastUpdated: Date.now(),
      });
    }

    return mockTabs;
  }

  /**
   * 运行完整性能测试套件
   */
  async runFullPerformanceTest(): Promise<void> {
    console.log('🚀 [PerformanceTest] 开始完整性能测试套件');

    this.startMonitoring();

    try {
      // 测试1: 小规模标签页状态比较
      console.log('\n📋 测试1: 小规模标签页状态比较 (10个标签页)');
      const smallTabs = this.generateMockTabs(10);
      const smallTabsModified = [...smallTabs];
      smallTabsModified[0] = { ...smallTabsModified[0], isActive: false };
      
      await this.testStateComparisonPerformance(smallTabs, smallTabsModified, 1000);

      // 测试2: 中等规模标签页状态比较
      console.log('\n📋 测试2: 中等规模标签页状态比较 (50个标签页)');
      const mediumTabs = this.generateMockTabs(50);
      const mediumTabsModified = [...mediumTabs];
      mediumTabsModified[0] = { ...mediumTabsModified[0], isActive: false };
      
      await this.testStateComparisonPerformance(mediumTabs, mediumTabsModified, 500);

      // 测试3: 大规模标签页状态比较
      console.log('\n📋 测试3: 大规模标签页状态比较 (100个标签页)');
      const largeTabs = this.generateMockTabs(100);
      const largeTabsModified = [...largeTabs];
      largeTabsModified[0] = { ...largeTabsModified[0], isActive: false };
      
      await this.testStateComparisonPerformance(largeTabs, largeTabsModified, 200);

      // 生成最终报告
      console.log('\n📊 性能测试完成，生成报告...');
      const report = this.getPerformanceReport();
      console.log('\n' + report.summary);
      
      if (report.recommendations.length > 0) {
        console.log('\n💡 优化建议:');
        report.recommendations.forEach(rec => console.log(`  - ${rec}`));
      }

    } finally {
      this.stopMonitoring();
    }
  }

  /**
   * 获取当前内存使用情况
   */
  private getCurrentMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
    }
    return 0;
  }

  /**
   * 开始内存监控
   */
  private startMemoryMonitoring(): void {
    // 每5秒记录一次内存使用情况
    const memoryInterval = setInterval(() => {
      if (!this.isMonitoring) {
        clearInterval(memoryInterval);
        return;
      }

      const memoryUsage = this.getCurrentMemoryUsage();
      console.log(`💾 [PerformanceTest] 内存使用: ${memoryUsage.toFixed(2)}MB`);
    }, 5000);
  }
}

// 导出单例实例
export const performanceTestManager = PerformanceTestManager.getInstance();

// 全局暴露，便于控制台调用
(window as any).PerformanceTestManager = PerformanceTestManager;
(window as any).performanceTestManager = performanceTestManager;

console.log('📊 [PerformanceTest] 性能测试工具已加载');
console.log('💡 使用方法: performanceTestManager.runFullPerformanceTest()');
