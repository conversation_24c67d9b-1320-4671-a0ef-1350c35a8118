/**
 * TabInfo类型转换工具
 * 确保新旧TabInfo接口的兼容性
 */

import { TabInfo } from '@/types/workspace';

/**
 * 从Chrome标签页创建增强的TabInfo对象
 */
export function createTabInfoFromChromeTab(
  chromeTab: chrome.tabs.Tab,
  additionalInfo: {
    isOpen?: boolean;
    isWorkspaceCore?: boolean;
    workonaId?: string;
  } = {}
): TabInfo {
  return {
    // 基础标识字段
    id: chromeTab.id!,
    chromeTabId: chromeTab.id!,
    url: chromeTab.url || '',
    title: chromeTab.title || '',
    favicon: chromeTab.favIconUrl || '',
    
    // 状态字段
    isOpen: additionalInfo.isOpen ?? true, // 如果能获取到Chrome标签页，说明是打开的
    isActive: chromeTab.active,
    isPinned: chromeTab.pinned,
    isWorkspaceCore: additionalInfo.isWorkspaceCore ?? false,
    
    // 位置信息
    windowId: chromeTab.windowId,
    index: chromeTab.index,
    
    // 扩展字段
    workonaId: additionalInfo.workonaId,
    lastUpdated: Date.now(),
  };
}

/**
 * 从旧版TabInfo创建新版TabInfo对象
 * 用于向后兼容
 */
export function upgradeTabInfo(
  oldTabInfo: Partial<TabInfo>,
  additionalInfo: {
    isOpen?: boolean;
    isWorkspaceCore?: boolean;
    workonaId?: string;
  } = {}
): TabInfo {
  return {
    // 基础标识字段
    id: oldTabInfo.id!,
    chromeTabId: oldTabInfo.chromeTabId || oldTabInfo.id!,
    url: oldTabInfo.url || '',
    title: oldTabInfo.title || '',
    favicon: oldTabInfo.favicon || '',
    
    // 状态字段
    isOpen: additionalInfo.isOpen ?? oldTabInfo.isOpen ?? true,
    isActive: oldTabInfo.isActive ?? false,
    isPinned: oldTabInfo.isPinned ?? false,
    isWorkspaceCore: additionalInfo.isWorkspaceCore ?? oldTabInfo.isWorkspaceCore ?? false,
    
    // 位置信息
    windowId: oldTabInfo.windowId || 0,
    index: oldTabInfo.index || 0,
    
    // 扩展字段
    workonaId: additionalInfo.workonaId || oldTabInfo.workonaId,
    lastUpdated: oldTabInfo.lastUpdated || Date.now(),
  };
}

/**
 * 检查TabInfo对象是否包含所有必需字段
 */
export function validateTabInfo(tabInfo: any): tabInfo is TabInfo {
  return (
    typeof tabInfo === 'object' &&
    typeof tabInfo.id === 'number' &&
    typeof tabInfo.chromeTabId === 'number' &&
    typeof tabInfo.url === 'string' &&
    typeof tabInfo.title === 'string' &&
    typeof tabInfo.favicon === 'string' &&
    typeof tabInfo.isOpen === 'boolean' &&
    typeof tabInfo.isActive === 'boolean' &&
    typeof tabInfo.isPinned === 'boolean' &&
    typeof tabInfo.isWorkspaceCore === 'boolean' &&
    typeof tabInfo.windowId === 'number' &&
    typeof tabInfo.index === 'number'
  );
}

/**
 * 深度比较两个TabInfo对象的关键状态字段
 */
export function isTabStateEqual(tab1: TabInfo, tab2: TabInfo): boolean {
  return (
    tab1.id === tab2.id &&
    tab1.chromeTabId === tab2.chromeTabId &&
    tab1.url === tab2.url &&
    tab1.title === tab2.title &&
    tab1.isOpen === tab2.isOpen &&
    tab1.isActive === tab2.isActive &&
    tab1.isPinned === tab2.isPinned &&
    tab1.isWorkspaceCore === tab2.isWorkspaceCore &&
    tab1.windowId === tab2.windowId &&
    tab1.index === tab2.index &&
    tab1.workonaId === tab2.workonaId
  );
}

/**
 * 创建TabInfo的快照，用于状态比较
 */
export function createTabInfoSnapshot(tabInfo: TabInfo): TabInfo {
  return {
    ...tabInfo,
    lastUpdated: Date.now(),
  };
}

/**
 * 批量转换TabInfo数组
 */
export function batchUpgradeTabInfos(
  oldTabInfos: Partial<TabInfo>[],
  defaultAdditionalInfo: {
    isOpen?: boolean;
    isWorkspaceCore?: boolean;
  } = {}
): TabInfo[] {
  return oldTabInfos.map(oldTabInfo => 
    upgradeTabInfo(oldTabInfo, defaultAdditionalInfo)
  );
}

console.log('📦 [TabInfoConverter] 标签页类型转换工具已加载');
