import {
  WorkspaceTab,
  TabIdMapping,
  OperationResult
} from '@/types/workspace';
import { StorageManager } from './storage';
import { ERROR_CODES } from './constants';

/**
 * Workona 风格标签页ID管理器
 * 实现 t-{workspaceId}-{uuid} 格式的标签页ID管理和映射
 */
export class WorkonaTabManager {
  /**
   * 生成 Workona 风格标签页ID
   * 格式：t-{workspaceId}-{uuid}
   */
  static generateWorkonaTabId(workspaceId: string): string {
    // 生成 UUID v4 格式的随机ID
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
    
    return `t-${workspaceId}-${uuid}`;
  }

  /**
   * 创建标签页ID映射关系（简化版：只保留工作区专属标签页）
   */
  static async createTabIdMapping(
    workonaId: string,
    chromeId: number,
    workspaceId: string,
    websiteId?: string,
    options?: {
      source?: 'workspace_website' | 'user_opened' | 'session_restored';
      sessionId?: string;
      originalUrl?: string;
    }
  ): Promise<OperationResult<TabIdMapping>> {
    try {
      // 验证workonaId格式和workspaceId一致性
      const workonaIdParts = workonaId.split('-');
      if (workonaIdParts.length !== 3 || workonaIdParts[0] !== 't' || workonaIdParts[1] !== workspaceId) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'Invalid workonaId format or workspaceId mismatch',
            details: { workonaId, workspaceId }
          }
        };
      }

      const source = options?.source ?? (websiteId ? 'workspace_website' : 'user_opened');

      const mapping: TabIdMapping = {
        workonaId,
        chromeId,
        workspaceId,
        websiteId,
        createdAt: Date.now(),
        lastSyncAt: Date.now(),

        // 简化元数据：只保留工作区专属标签页概念
        metadata: {
          source,
          sessionId: options?.sessionId,
          originalUrl: options?.originalUrl,
          addedToWorkspaceAt: Date.now(),
          isPinned: false, // 初始化为未固定，后续会根据实际状态更新
          pinnedAt: undefined,
          unpinnedAt: undefined
        }
      };

      // 获取现有映射
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;

      // 检查是否已存在相同的映射
      const existingIndex = mappings.findIndex(m =>
        m.workonaId === workonaId || m.chromeId === chromeId
      );

      if (existingIndex >= 0) {
        // 更新现有映射，保留重要元数据
        const existing = mappings[existingIndex];
        mappings[existingIndex] = {
          ...mapping,
          createdAt: existing.createdAt, // 保留原始创建时间
          metadata: {
            ...existing.metadata,
            ...mapping.metadata,
            source: mapping.metadata?.source || existing.metadata?.source || 'user_opened',
            addedToWorkspaceAt: existing.metadata?.addedToWorkspaceAt || mapping.metadata?.addedToWorkspaceAt
          }
        };
      } else {
        // 添加新映射
        mappings.push(mapping);
      }

      // 保存映射
      const saveResult = await StorageManager.saveTabIdMappings(mappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      // 将 Workona ID 存储到标签页的会话存储中（用于浏览器重启后的血缘跟踪）
      try {
        // 获取标签页的当前固定状态
        const tab = await chrome.tabs.get(chromeId);
        const isPinned = tab.pinned;

        await chrome.scripting.executeScript({
          target: { tabId: chromeId },
          func: (workonaId: string, workspaceId: string, websiteId: string, isPinned: boolean) => {
            const workonaData = {
              workonaId,
              workspaceId,
              websiteId,
              isPinned,
              timestamp: Date.now()
            };
            sessionStorage.setItem('workonaData', JSON.stringify(workonaData));
            console.log(`📝 标签页会话存储 Workona 数据:`, workonaData);
          },
          args: [workonaId, workspaceId, websiteId || '', isPinned]
        });
      } catch (error) {
        // 如果无法注入脚本（如系统页面），记录但不影响主流程
        console.warn(`⚠️ 无法为标签页 ${chromeId} 设置会话存储:`, error);
      }

      console.log(`✅ 创建标签页ID映射: ${workonaId} <-> ${chromeId} (工作区专属标签页)`);
      return { success: true, data: mapping };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to create tab ID mapping',
          details: error,
        },
      };
    }
  }

  /**
   * 根据 Chrome ID 获取 Workona ID
   */
  static async getWorkonaIdByChromeId(chromeId: number): Promise<OperationResult<string | null>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const mapping = mappings.find(m => m.chromeId === chromeId);
      
      return { success: true, data: mapping?.workonaId || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get Workona ID by Chrome ID',
          details: error,
        },
      };
    }
  }

  /**
   * 根据 Workona ID 获取 Chrome ID
   */
  static async getChromeIdByWorkonaId(workonaId: string): Promise<OperationResult<number | null>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const mapping = mappings.find(m => m.workonaId === workonaId);

      return { success: true, data: mapping?.chromeId || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get Chrome ID by Workona ID',
          details: error,
        },
      };
    }
  }



  /**
   * 同步标签页映射关系
   * 清理无效的映射，更新现有映射的同步时间
   */
  static async syncTabMappings(): Promise<OperationResult<number>> {
    try {
      console.log('🔄 开始同步标签页映射关系...');
      
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const validMappings: TabIdMapping[] = [];
      let cleanedCount = 0;

      // 获取所有当前存在的标签页
      const allTabs = await chrome.tabs.query({});
      const existingChromeIds = new Set(allTabs.map(tab => tab.id!));

      // 检查每个映射的有效性
      for (const mapping of mappings) {
        if (existingChromeIds.has(mapping.chromeId)) {
          // 标签页仍然存在，更新同步时间
          mapping.lastSyncAt = Date.now();
          validMappings.push(mapping);
        } else {
          // 标签页已不存在，清理映射
          console.log(`🗑️ 清理无效映射: ${mapping.workonaId} <-> ${mapping.chromeId}`);
          cleanedCount++;
        }
      }

      // 保存清理后的映射
      const saveResult = await StorageManager.saveTabIdMappings(validMappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ 标签页映射同步完成，清理了 ${cleanedCount} 个无效映射`);
      return { success: true, data: cleanedCount };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to sync tab mappings',
          details: error,
        },
      };
    }
  }

  /**
   * 删除标签页映射
   */
  static async removeTabMapping(workonaId: string): Promise<OperationResult<void>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const filteredMappings = mappings.filter(m => m.workonaId !== workonaId);

      const saveResult = await StorageManager.saveTabIdMappings(filteredMappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`🗑️ 删除标签页映射: ${workonaId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to remove tab mapping',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区的所有 Workona 标签页ID
   */
  static async getWorkspaceWorkonaTabIds(workspaceId: string): Promise<OperationResult<string[]>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const workonaIds = mappings
        .filter(m => m.workspaceId === workspaceId)
        .map(m => m.workonaId);

      return { success: true, data: workonaIds };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace Workona tab IDs',
          details: error,
        },
      };
    }
  }

  /**
   * 批量清理工作区的标签页映射
   */
  static async clearWorkspaceTabMappings(workspaceId: string): Promise<OperationResult<number>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const remainingMappings = mappings.filter(m => m.workspaceId !== workspaceId);
      const clearedCount = mappings.length - remainingMappings.length;

      const saveResult = await StorageManager.saveTabIdMappings(remainingMappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`🗑️ 清理工作区 ${workspaceId} 的 ${clearedCount} 个标签页映射`);
      return { success: true, data: clearedCount };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to clear workspace tab mappings',
          details: error,
        },
      };
    }
  }

  // === 概念性重构：标签页元数据管理方法 ===

  /**
   * 获取标签页元数据
   */
  static async getTabMetadata(workonaId: string): Promise<OperationResult<TabIdMapping | null>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const mapping = mappings.find(m => m.workonaId === workonaId);

      return { success: true, data: mapping || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get tab metadata',
          details: error,
        },
      };
    }
  }

  /**
   * 更新标签页元数据
   */
  static async updateTabMetadata(
    workonaId: string,
    updates: Partial<Pick<TabIdMapping, 'metadata'>>
  ): Promise<OperationResult<TabIdMapping>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;
      const mappingIndex = mappings.findIndex(m => m.workonaId === workonaId);

      if (mappingIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'Tab mapping not found',
          },
        };
      }

      // 更新映射
      const existingMapping = mappings[mappingIndex];
      const updatedMapping: TabIdMapping = {
        ...existingMapping,
        ...updates,
        lastSyncAt: Date.now(),
        metadata: {
          ...existingMapping.metadata,
          ...updates.metadata,
          // 确保 source 字段始终有值
          source: updates.metadata?.source || existingMapping.metadata?.source || 'user_opened'
        }
      };

      mappings[mappingIndex] = updatedMapping;

      const saveResult = await StorageManager.saveTabIdMappings(mappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`📝 更新标签页元数据: ${workonaId}`);
      return { success: true, data: updatedMapping };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to update tab metadata',
          details: error,
        },
      };
    }
  }

  // 删除promoteToWorkspaceCore方法 - 不再需要核心/会话标签页转换

  // 删除demoteToSessionTab方法 - 不再需要核心/会话标签页转换

  /**
   * 同步标签页编辑后的状态
   * 确保编辑标签页后不会破坏 Workona ID 映射关系
   */
  static async syncTabAfterEdit(chromeId: number, newUrl?: string, newTitle?: string): Promise<OperationResult<void>> {
    try {
      // 获取标签页的 Workona ID
      const workonaIdResult = await this.getWorkonaIdByChromeId(chromeId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        // 标签页没有 Workona ID，可能是系统标签页，跳过同步
        return { success: true };
      }

      const workonaId = workonaIdResult.data;

      // 获取当前标签页元数据
      const metadataResult = await this.getTabMetadata(workonaId);
      if (!metadataResult.success || !metadataResult.data) {
        console.warn(`无法获取标签页元数据: ${workonaId}`);
        return { success: true };
      }

      const currentMetadata = metadataResult.data;

      // 更新元数据中的URL信息（如果有变化）
      if (newUrl && newUrl !== currentMetadata.metadata?.originalUrl) {
        const updates: Partial<TabIdMapping> = {
          metadata: {
            ...currentMetadata.metadata,
            source: currentMetadata.metadata?.source || 'user_opened',
            originalUrl: newUrl,
            // 记录编辑时间
            lastEditedAt: Date.now()
          }
        };

        const updateResult = await this.updateTabMetadata(workonaId, updates);
        if (updateResult.success) {
          console.log(`🔄 同步标签页编辑: ${workonaId} (新URL: ${newUrl})`);
        }
      }

      // 同步到会话管理器
      const { WorkspaceSessionManager } = await import('./workspaceSessionManager');
      const currentSession = WorkspaceSessionManager.getCurrentSession();

      if (currentSession && currentSession.tabs[workonaId]) {
        const updatedTab = {
          ...currentSession.tabs[workonaId],
          url: newUrl || currentSession.tabs[workonaId].url,
          title: newTitle || currentSession.tabs[workonaId].title,
          lastUpdated: Date.now()
        };

        await WorkspaceSessionManager.updateSessionTab(workonaId, updatedTab);
        console.log(`📝 同步标签页到会话: ${workonaId}`);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to sync tab after edit',
          details: error,
        },
      };
    }
  }

  /**
   * 检查标签页是否为工作区专属标签页
   */
  static async isWorkspaceSpecific(chromeId: number): Promise<OperationResult<boolean>> {
    try {
      const workonaIdResult = await this.getWorkonaIdByChromeId(chromeId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        return { success: true, data: false }; // 无 Workona ID = 非工作区专属标签页
      }

      // 有Workona ID就是工作区专属标签页
      return { success: true, data: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to check if tab is workspace specific',
          details: error,
        },
      };
    }
  }

  // 删除getWorkspaceCoreTabIds和getWorkspaceSessionTabIds方法 - 不再区分核心和会话标签页

  /**
   * 检查工作区网站是否有对应的打开标签页
   */
  static async isWebsiteTabOpen(
    workspaceId: string,
    websiteId: string
  ): Promise<OperationResult<{ isOpen: boolean; chromeId?: number; workonaId?: string }>> {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }

      const mappings = mappingsResult.data!;

      // 查找对应的映射
      const mapping = mappings.find(m =>
        m.workspaceId === workspaceId &&
        m.websiteId === websiteId
        // 所有有Workona ID的标签页都是工作区专属标签页
      );

      if (!mapping) {
        return {
          success: true,
          data: { isOpen: false }
        };
      }

      // 检查对应的 Chrome 标签页是否存在
      try {
        const tab = await chrome.tabs.get(mapping.chromeId);
        if (tab) {
          return {
            success: true,
            data: {
              isOpen: true,
              chromeId: mapping.chromeId,
              workonaId: mapping.workonaId
            }
          };
        }
      } catch {
        // 标签页不存在，清理无效映射
        await this.removeTabMapping(mapping.workonaId);
        console.log(`🗑️ 清理无效的标签页映射: ${mapping.workonaId}`);
      }

      return {
        success: true,
        data: { isOpen: false }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to check if website tab is open',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区所有网站的标签页状态
   */
  static async getWorkspaceWebsiteTabStates(
    workspaceId: string,
    websiteIds: string[]
  ): Promise<OperationResult<Record<string, { isOpen: boolean; chromeId?: number; workonaId?: string }>>> {
    try {
      const result: Record<string, { isOpen: boolean; chromeId?: number; workonaId?: string }> = {};

      // 批量检查所有网站的标签页状态
      for (const websiteId of websiteIds) {
        const statusResult = await this.isWebsiteTabOpen(workspaceId, websiteId);
        if (statusResult.success) {
          result[websiteId] = statusResult.data!;
        } else {
          result[websiteId] = { isOpen: false };
        }
      }

      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace website tab states',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区所有专属标签页
   * 返回属于指定工作区的所有专属标签页列表
   */
  static async getWorkspaceAllTabs(workspaceId: string): Promise<OperationResult<{
    workspaceSpecificTabs: WorkspaceTab[];
    totalCount: number;
  }>> {
    try {
      console.log(`🔍 获取工作区 ${workspaceId} 的所有专属标签页`);

      // 获取当前窗口所有标签页
      const allTabs = await chrome.tabs.query({ currentWindow: true });
      const workspaceSpecificTabs: WorkspaceTab[] = [];

      for (const tab of allTabs) {
        if (!tab.id) continue;

        try {
          // 检查标签页是否有 Workona ID 映射
          const workonaIdResult = await this.getWorkonaIdByChromeId(tab.id);
          if (!workonaIdResult.success || !workonaIdResult.data) {
            continue; // 没有 Workona ID，跳过
          }

          const workonaId = workonaIdResult.data;

          // 检查是否属于当前工作区
          const workspaceIdFromMapping = workonaId.split('-')[1];
          if (workspaceIdFromMapping !== workspaceId) {
            continue; // 不属于当前工作区，跳过
          }

          // 获取标签页元数据
          const metadataResult = await this.getTabMetadata(workonaId);
          if (!metadataResult.success || !metadataResult.data) {
            continue; // 无法获取元数据，跳过
          }

          // 构建 WorkspaceTab 对象
          const workspaceTab: WorkspaceTab = {
            id: workonaId,
            chromeTabId: tab.id,
            workspaceId,
            url: tab.url || '',
            title: tab.title || '',
            favicon: tab.favIconUrl || '',
            isActive: tab.active,
            isPinned: tab.pinned,
            windowId: tab.windowId,
            index: tab.index,
            discarded: tab.discarded,
            createdAt: metadataResult.data.createdAt,
            lastActiveAt: tab.active ? Date.now() : metadataResult.data.lastSyncAt,
          };

          // 所有有Workona ID的标签页都是工作区专属标签页
          workspaceSpecificTabs.push(workspaceTab);

          console.log(`✅ 找到工作区专属标签页: ${tab.title}`);
        } catch (error) {
          console.warn(`⚠️ 处理标签页 ${tab.id} 时出错:`, error);
          continue;
        }
      }

      // 按索引排序
      workspaceSpecificTabs.sort((a, b) => a.index - b.index);

      const result = {
        workspaceSpecificTabs,
        totalCount: workspaceSpecificTabs.length,
      };

      console.log(`📊 工作区专属标签页统计: 总计${result.totalCount}个`);
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace all tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 根据 Workona ID 获取单个标签页信息
   */
  static async getWorkspaceTabById(workonaId: string): Promise<OperationResult<WorkspaceTab | null>> {
    try {
      // 获取 Chrome 标签页ID
      const chromeIdResult = await this.getChromeIdByWorkonaId(workonaId);
      if (!chromeIdResult.success || !chromeIdResult.data) {
        return { success: true, data: null }; // 标签页不存在
      }

      const chromeId = chromeIdResult.data;

      // 获取 Chrome 标签页信息
      const tab = await chrome.tabs.get(chromeId);
      if (!tab) {
        return { success: true, data: null };
      }

      // 获取元数据
      const metadataResult = await this.getTabMetadata(workonaId);
      if (!metadataResult.success || !metadataResult.data) {
        return { success: true, data: null };
      }

      const workspaceId = workonaId.split('-')[1];
      const workspaceTab: WorkspaceTab = {
        id: workonaId,
        chromeTabId: tab.id!,
        workspaceId,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isActive: tab.active,
        isPinned: tab.pinned,
        windowId: tab.windowId,
        index: tab.index,
        discarded: tab.discarded,
        createdAt: metadataResult.data.createdAt,
        lastActiveAt: tab.active ? Date.now() : metadataResult.data.lastSyncAt,
      };

      return { success: true, data: workspaceTab };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace tab by id',
          details: error,
        },
      };
    }
  }

  // 删除convertTabType方法 - 不再需要核心/会话标签页转换

  /**
   * 批量获取标签页状态（包含打开状态检查）
   */
  static async getWorkspaceTabsWithStates(workspaceId: string): Promise<OperationResult<{
    workspaceSpecificTabs: Array<WorkspaceTab & { isOpen: boolean }>;
    totalCount: number;
    openCount: number;
  }>> {
    try {
      // 获取所有标签页
      const tabsResult = await this.getWorkspaceAllTabs(workspaceId);
      if (!tabsResult.success) {
        return tabsResult as any;
      }

      const { workspaceSpecificTabs } = tabsResult.data!;

      // 检查每个标签页的打开状态
      const tabsWithStates = await Promise.all(
        workspaceSpecificTabs.map(async (tab) => {
          try {
            await chrome.tabs.get(tab.chromeTabId);
            return { ...tab, isOpen: true };
          } catch {
            return { ...tab, isOpen: false };
          }
        })
      );

      const totalCount = tabsWithStates.length;
      const openCount = tabsWithStates.filter(t => t.isOpen).length;

      return {
        success: true,
        data: {
          workspaceSpecificTabs: tabsWithStates,
          totalCount,
          openCount,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace tabs with states',
          details: error,
        },
      };
    }
  }
}
