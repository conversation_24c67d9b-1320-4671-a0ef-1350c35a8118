import { OperationResult } from '@/types/workspace';
import { ERROR_CODES } from './constants';
import { WorkonaTabManager } from './workonaTabManager';

/**
 * 窗口保护管理器
 * 实现智能窗口保护机制，防止窗口因为没有标签页而被Chrome自动关闭
 */
export class WindowProtectionManager {
  
  /**
   * 检查当前窗口是否需要保护
   * 只有在当前Chrome窗口中只剩下1个用户标签页或1个工作区专属标签页时才需要保护
   */
  static async shouldProtectWindow(windowId?: number): Promise<OperationResult<{
    needsProtection: boolean;
    reason: string;
    tabCounts: {
      totalTabs: number;
      userTabs: number;
      coreWorkspaceTabs: number;
      systemTabs: number;
      pinnedTabs: number;
    };
  }>> {
    try {
      // 获取目标窗口ID
      const targetWindowId = windowId || (await chrome.windows.getCurrent()).id;
      
      // 获取窗口中的所有标签页
      const allTabs = await chrome.tabs.query({ windowId: targetWindowId });
      
      console.log(`🔍 [WindowProtection] 检查窗口 ${targetWindowId} 的保护需求，总标签页数: ${allTabs.length}`);
      
      let userTabs = 0;
      let coreWorkspaceTabs = 0;
      let systemTabs = 0;
      let pinnedTabs = 0;
      
      // 分析每个标签页的类型
      for (const tab of allTabs) {
        if (!tab.id) continue;
        
        // 统计固定标签页
        if (tab.pinned) {
          pinnedTabs++;
        }
        
        // 检查是否为系统标签页
        if (this.isSystemTab(tab)) {
          systemTabs++;
          console.log(`🔧 系统标签页: ${tab.title} (${tab.url})`);
          continue;
        }
        
        // 检查是否有Workona ID映射
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        
        if (workonaIdResult.success && workonaIdResult.data) {
          // 有Workona ID，检查是否为核心标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
          
          if (metadataResult.success && metadataResult.data) {
            if (metadataResult.data.isWorkspaceCore) {
              coreWorkspaceTabs++;
              console.log(`🏢 工作区核心标签页: ${tab.title} (${tab.url})`);
            } else {
              userTabs++;
              console.log(`👤 用户会话标签页: ${tab.title} (${tab.url})`);
            }
          } else {
            // 有Workona ID但无法获取元数据，默认为用户标签页
            userTabs++;
            console.log(`👤 用户标签页(无元数据): ${tab.title} (${tab.url})`);
          }
        } else {
          // 没有Workona ID，检查是否为普通网页
          if (this.isUserTab(tab)) {
            userTabs++;
            console.log(`👤 用户标签页(无Workona ID): ${tab.title} (${tab.url})`);
          } else {
            systemTabs++;
            console.log(`🔧 系统标签页(无Workona ID): ${tab.title} (${tab.url})`);
          }
        }
      }
      
      const tabCounts = {
        totalTabs: allTabs.length,
        userTabs,
        coreWorkspaceTabs,
        systemTabs,
        pinnedTabs,
      };
      
      console.log(`📊 [WindowProtection] 标签页统计:`, tabCounts);
      
      // 判断是否需要保护
      let needsProtection = false;
      let reason = '';

      // 计算有效标签页数量（排除系统标签页）
      const effectiveTabs = userTabs + coreWorkspaceTabs;

      // 🛡️ 更严格的保护条件：只有在即将没有任何有效标签页时才保护
      if (effectiveTabs === 0) {
        needsProtection = true;
        reason = '窗口中没有有效的用户标签页或工作区标签页，需要保护防止窗口关闭';
      } else if (effectiveTabs === 1) {
        // 只有在只剩1个有效标签页且即将被移除时才需要保护
        // 这个逻辑应该在实际移除操作前调用，而不是在一般检查时
        needsProtection = false;
        reason = `窗口中还有${effectiveTabs}个有效标签页，暂时无需保护`;
      } else {
        needsProtection = false;
        reason = `窗口中有${effectiveTabs}个有效标签页，无需保护`;
      }
      
      console.log(`🛡️ [WindowProtection] 保护判断: ${needsProtection ? '需要保护' : '无需保护'} - ${reason}`);
      
      return {
        success: true,
        data: {
          needsProtection,
          reason,
          tabCounts,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to check window protection needs',
          details: error,
        },
      };
    }
  }
  
  /**
   * 创建保护标签页
   * 在需要保护窗口时创建一个新的空白标签页
   */
  static async createProtectionTab(windowId?: number): Promise<OperationResult<number>> {
    try {
      const targetWindowId = windowId || (await chrome.windows.getCurrent()).id;
      
      console.log(`🛡️ [WindowProtection] 为窗口 ${targetWindowId} 创建保护标签页`);
      
      // 创建新的空白标签页
      const newTab = await chrome.tabs.create({
        windowId: targetWindowId,
        url: 'chrome://newtab/',
        active: false, // 不激活，避免干扰用户
        pinned: false,
      });
      
      if (!newTab.id) {
        throw new Error('Failed to create protection tab');
      }
      
      console.log(`✅ [WindowProtection] 成功创建保护标签页: ${newTab.id}`);
      
      return {
        success: true,
        data: newTab.id,
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to create protection tab',
          details: error,
        },
      };
    }
  }
  
  /**
   * 检查在移除指定标签页后是否需要保护窗口
   */
  static async shouldProtectAfterRemovingTabs(
    tabIdsToRemove: number[],
    windowId?: number
  ): Promise<OperationResult<{
    needsProtection: boolean;
    reason: string;
    tabCounts: {
      totalTabs: number;
      userTabs: number;
      coreWorkspaceTabs: number;
      systemTabs: number;
      pinnedTabs: number;
    };
  }>> {
    try {
      // 获取目标窗口ID
      const targetWindowId = windowId || (await chrome.windows.getCurrent()).id;

      // 获取窗口中的所有标签页
      const allTabs = await chrome.tabs.query({ windowId: targetWindowId });

      console.log(`🔍 [WindowProtection] 检查移除 ${tabIdsToRemove.length} 个标签页后的保护需求`);

      let userTabs = 0;
      let coreWorkspaceTabs = 0;
      let systemTabs = 0;
      let pinnedTabs = 0;

      // 分析移除后剩余的标签页
      for (const tab of allTabs) {
        if (!tab.id || tabIdsToRemove.includes(tab.id)) {
          continue; // 跳过即将被移除的标签页
        }

        // 统计固定标签页
        if (tab.pinned) {
          pinnedTabs++;
        }

        // 检查是否为系统标签页
        if (this.isSystemTab(tab)) {
          systemTabs++;
          continue;
        }

        // 检查是否有Workona ID映射
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);

        if (workonaIdResult.success && workonaIdResult.data) {
          // 有Workona ID，检查是否为核心标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);

          if (metadataResult.success && metadataResult.data) {
            if (metadataResult.data.isWorkspaceCore) {
              coreWorkspaceTabs++;
            } else {
              userTabs++;
            }
          } else {
            userTabs++;
          }
        } else {
          // 没有Workona ID，检查是否为普通网页
          if (this.isUserTab(tab)) {
            userTabs++;
          } else {
            systemTabs++;
          }
        }
      }

      const tabCounts = {
        totalTabs: allTabs.length - tabIdsToRemove.length,
        userTabs,
        coreWorkspaceTabs,
        systemTabs,
        pinnedTabs,
      };

      console.log(`📊 [WindowProtection] 移除后标签页统计:`, tabCounts);

      // 判断是否需要保护
      const effectiveTabs = userTabs + coreWorkspaceTabs;
      let needsProtection = false;
      let reason = '';

      if (effectiveTabs === 0) {
        needsProtection = true;
        reason = '移除标签页后窗口将没有有效标签页，需要保护防止窗口关闭';
      } else {
        needsProtection = false;
        reason = `移除标签页后窗口仍有${effectiveTabs}个有效标签页，无需保护`;
      }

      console.log(`🛡️ [WindowProtection] 移除后保护判断: ${needsProtection ? '需要保护' : '无需保护'} - ${reason}`);

      return {
        success: true,
        data: {
          needsProtection,
          reason,
          tabCounts,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to check protection needs after removing tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 智能窗口保护
   * 检查窗口是否需要保护，如果需要则创建保护标签页
   */
  static async protectWindowIfNeeded(windowId?: number): Promise<OperationResult<{
    protected: boolean;
    protectionTabId?: number;
    reason: string;
  }>> {
    try {
      // 检查是否需要保护
      const checkResult = await this.shouldProtectWindow(windowId);
      if (!checkResult.success) {
        return {
          success: false,
          error: checkResult.error,
        };
      }
      
      const { needsProtection, reason } = checkResult.data!;
      
      if (!needsProtection) {
        return {
          success: true,
          data: {
            protected: false,
            reason: '窗口无需保护',
          },
        };
      }
      
      // 创建保护标签页
      const createResult = await this.createProtectionTab(windowId);
      if (!createResult.success) {
        return {
          success: false,
          error: createResult.error,
        };
      }
      
      return {
        success: true,
        data: {
          protected: true,
          protectionTabId: createResult.data!,
          reason,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to protect window',
          details: error,
        },
      };
    }
  }

  /**
   * 在移除标签页前进行智能保护
   * 检查移除指定标签页后是否需要保护，如果需要则创建保护标签页
   */
  static async protectBeforeRemovingTabs(
    tabIdsToRemove: number[],
    windowId?: number
  ): Promise<OperationResult<{
    protected: boolean;
    protectionTabId?: number;
    reason: string;
  }>> {
    try {
      // 检查移除后是否需要保护
      const checkResult = await this.shouldProtectAfterRemovingTabs(tabIdsToRemove, windowId);
      if (!checkResult.success) {
        return {
          success: false,
          error: checkResult.error,
        };
      }

      const { needsProtection, reason } = checkResult.data!;

      if (!needsProtection) {
        return {
          success: true,
          data: {
            protected: false,
            reason,
          },
        };
      }

      // 创建保护标签页
      const createResult = await this.createProtectionTab(windowId);
      if (!createResult.success) {
        return {
          success: false,
          error: createResult.error,
        };
      }

      return {
        success: true,
        data: {
          protected: true,
          protectionTabId: createResult.data!,
          reason,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to protect before removing tabs',
          details: error,
        },
      };
    }
  }
  
  /**
   * 检查是否为系统标签页
   */
  private static isSystemTab(tab: chrome.tabs.Tab): boolean {
    if (!tab.url) return true;
    
    return (
      tab.url.includes('chrome://') ||
      tab.url.includes('chrome-extension://') ||
      tab.url.includes('edge://') ||
      tab.url.includes('about:') ||
      tab.url.includes('workspace-placeholder.html') ||
      tab.url === 'chrome://newtab/' ||
      tab.url === 'about:blank' ||
      tab.url === ''
    );
  }
  
  /**
   * 检查是否为用户标签页（没有Workona ID的普通网页）
   */
  private static isUserTab(tab: chrome.tabs.Tab): boolean {
    if (!tab.url) return false;
    
    // 排除系统页面
    if (this.isSystemTab(tab)) return false;
    
    // 普通网页被认为是用户标签页
    return true;
  }
}
