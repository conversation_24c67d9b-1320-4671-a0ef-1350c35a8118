/**
 * Chrome标签页事件管理器
 * 实现事件驱动的标签页状态管理，替换定时器轮询
 */

import { TabInfo } from '@/types/workspace';
import { createTabInfoFromChromeTab } from './tabInfoConverter';

type TabEventCallback = (eventType: 'created' | 'updated' | 'removed' | 'activated', data: any) => void;

/**
 * 标签页事件管理器类
 */
export class TabEventManager {
  private static instance: TabEventManager | null = null;
  private callbacks: Set<TabEventCallback> = new Set();
  private isListening = false;
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
  private batchUpdateTimer: NodeJS.Timeout | null = null;
  private pendingUpdates: Map<number, any> = new Map();

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): TabEventManager {
    if (!TabEventManager.instance) {
      TabEventManager.instance = new TabEventManager();
    }
    return TabEventManager.instance;
  }

  /**
   * 添加事件监听回调
   */
  addCallback(callback: TabEventCallback): () => void {
    this.callbacks.add(callback);
    
    // 如果这是第一个回调，开始监听事件
    if (this.callbacks.size === 1 && !this.isListening) {
      this.startListening();
    }

    // 返回移除回调的函数
    return () => {
      this.callbacks.delete(callback);
      
      // 如果没有回调了，停止监听
      if (this.callbacks.size === 0 && this.isListening) {
        this.stopListening();
      }
    };
  }

  /**
   * 开始监听Chrome标签页事件
   */
  private startListening(): void {
    if (this.isListening) return;

    console.log('🎧 [TabEventManager] 开始监听Chrome标签页事件');

    // 监听标签页创建事件
    chrome.tabs.onCreated.addListener(this.handleTabCreated);
    
    // 监听标签页更新事件
    chrome.tabs.onUpdated.addListener(this.handleTabUpdated);
    
    // 监听标签页移除事件
    chrome.tabs.onRemoved.addListener(this.handleTabRemoved);
    
    // 监听标签页激活事件
    chrome.tabs.onActivated.addListener(this.handleTabActivated);
    
    // 监听标签页移动事件
    chrome.tabs.onMoved.addListener(this.handleTabMoved);

    this.isListening = true;
  }

  /**
   * 停止监听Chrome标签页事件
   */
  private stopListening(): void {
    if (!this.isListening) return;

    console.log('🔇 [TabEventManager] 停止监听Chrome标签页事件');

    chrome.tabs.onCreated.removeListener(this.handleTabCreated);
    chrome.tabs.onUpdated.removeListener(this.handleTabUpdated);
    chrome.tabs.onRemoved.removeListener(this.handleTabRemoved);
    chrome.tabs.onActivated.removeListener(this.handleTabActivated);
    chrome.tabs.onMoved.removeListener(this.handleTabMoved);

    // 清理定时器
    this.debounceTimers.forEach(timer => clearTimeout(timer));
    this.debounceTimers.clear();
    
    if (this.batchUpdateTimer) {
      clearTimeout(this.batchUpdateTimer);
      this.batchUpdateTimer = null;
    }

    this.isListening = false;
  }

  /**
   * 处理标签页创建事件
   */
  private handleTabCreated = (tab: chrome.tabs.Tab): void => {
    console.log(`📝 [TabEventManager] 标签页创建: ${tab.title} (${tab.id})`);
    
    this.debounceEvent(`created-${tab.id}`, () => {
      this.notifyCallbacks('created', {
        tab: createTabInfoFromChromeTab(tab),
        chromeTab: tab,
      });
    }, 100);
  };

  /**
   * 处理标签页更新事件
   */
  private handleTabUpdated = (tabId: number, changeInfo: chrome.tabs.TabChangeInfo, tab: chrome.tabs.Tab): void => {
    // 只处理重要的变化
    const importantChanges = ['status', 'title', 'url', 'pinned', 'audible'];
    const hasImportantChange = Object.keys(changeInfo).some(key => importantChanges.includes(key));
    
    if (!hasImportantChange) return;

    console.log(`🔄 [TabEventManager] 标签页更新: ${tab.title} (${tabId})`, changeInfo);
    
    // 批量处理更新事件，避免频繁触发
    this.pendingUpdates.set(tabId, { changeInfo, tab });
    
    if (this.batchUpdateTimer) {
      clearTimeout(this.batchUpdateTimer);
    }
    
    this.batchUpdateTimer = setTimeout(() => {
      this.processBatchUpdates();
    }, 200);
  };

  /**
   * 处理标签页移除事件
   */
  private handleTabRemoved = (tabId: number, removeInfo: chrome.tabs.TabRemoveInfo): void => {
    console.log(`🗑️ [TabEventManager] 标签页移除: ${tabId}`);
    
    this.debounceEvent(`removed-${tabId}`, () => {
      this.notifyCallbacks('removed', {
        tabId,
        removeInfo,
      });
    }, 100);
  };

  /**
   * 处理标签页激活事件
   */
  private handleTabActivated = (activeInfo: chrome.tabs.TabActiveInfo): void => {
    console.log(`🎯 [TabEventManager] 标签页激活: ${activeInfo.tabId}`);
    
    this.debounceEvent(`activated-${activeInfo.tabId}`, () => {
      this.notifyCallbacks('activated', {
        tabId: activeInfo.tabId,
        windowId: activeInfo.windowId,
      });
    }, 50);
  };

  /**
   * 处理标签页移动事件
   */
  private handleTabMoved = (tabId: number, moveInfo: chrome.tabs.TabMoveInfo): void => {
    console.log(`📍 [TabEventManager] 标签页移动: ${tabId} -> 位置 ${moveInfo.toIndex}`);
    
    this.debounceEvent(`moved-${tabId}`, () => {
      this.notifyCallbacks('updated', {
        tabId,
        changeInfo: { index: moveInfo.toIndex },
        moveInfo,
      });
    }, 100);
  };

  /**
   * 处理批量更新
   */
  private processBatchUpdates(): void {
    if (this.pendingUpdates.size === 0) return;

    console.log(`📦 [TabEventManager] 处理批量更新: ${this.pendingUpdates.size} 个标签页`);

    for (const [tabId, { changeInfo, tab }] of this.pendingUpdates) {
      this.notifyCallbacks('updated', {
        tabId,
        changeInfo,
        tab: createTabInfoFromChromeTab(tab),
        chromeTab: tab,
      });
    }

    this.pendingUpdates.clear();
    this.batchUpdateTimer = null;
  }

  /**
   * 防抖事件处理
   */
  private debounceEvent(key: string, callback: () => void, delay: number): void {
    const existingTimer = this.debounceTimers.get(key);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    const timer = setTimeout(() => {
      callback();
      this.debounceTimers.delete(key);
    }, delay);

    this.debounceTimers.set(key, timer);
  }

  /**
   * 通知所有回调
   */
  private notifyCallbacks(eventType: 'created' | 'updated' | 'removed' | 'activated', data: any): void {
    for (const callback of this.callbacks) {
      try {
        callback(eventType, data);
      } catch (error) {
        console.error(`❌ [TabEventManager] 回调执行失败:`, error);
      }
    }
  }

  /**
   * 获取当前监听状态
   */
  isCurrentlyListening(): boolean {
    return this.isListening;
  }

  /**
   * 获取当前回调数量
   */
  getCallbackCount(): number {
    return this.callbacks.size;
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopListening();
    this.callbacks.clear();
    TabEventManager.instance = null;
  }
}

// 导出单例实例
export const tabEventManager = TabEventManager.getInstance();

console.log('🎧 [TabEventManager] 标签页事件管理器已加载');
