/**
 * 标签页状态深度比较工具
 * 解决基于索引比较的缺陷，支持标签页顺序变化
 */

import { TabInfo } from '@/types/workspace';
import { isTabStateEqual } from './tabInfoConverter';

/**
 * 创建基于标签页ID的状态映射表
 */
export function createTabStateMap(tabs: TabInfo[]): Map<number, TabInfo> {
  const map = new Map<number, TabInfo>();
  
  for (const tab of tabs) {
    // 使用chromeTabId作为主键，确保唯一性
    const key = tab.chromeTabId || tab.id;
    map.set(key, tab);
  }
  
  return map;
}

/**
 * 深度比较两个标签页状态映射表
 * 支持标签页顺序变化，只关注实际状态差异
 */
export function compareTabStateMaps(
  oldMap: Map<number, TabInfo>, 
  newMap: Map<number, TabInfo>
): {
  hasChanges: boolean;
  addedTabs: TabInfo[];
  removedTabs: TabInfo[];
  changedTabs: { old: TabInfo; new: TabInfo }[];
  unchangedTabs: TabInfo[];
} {
  const addedTabs: TabInfo[] = [];
  const removedTabs: TabInfo[] = [];
  const changedTabs: { old: TabInfo; new: TabInfo }[] = [];
  const unchangedTabs: TabInfo[] = [];

  // 检查新增和变化的标签页
  for (const [id, newTab] of newMap) {
    const oldTab = oldMap.get(id);
    
    if (!oldTab) {
      // 新增的标签页
      addedTabs.push(newTab);
    } else if (!isTabStateEqual(oldTab, newTab)) {
      // 状态发生变化的标签页
      changedTabs.push({ old: oldTab, new: newTab });
    } else {
      // 状态未变化的标签页
      unchangedTabs.push(newTab);
    }
  }

  // 检查被移除的标签页
  for (const [id, oldTab] of oldMap) {
    if (!newMap.has(id)) {
      removedTabs.push(oldTab);
    }
  }

  const hasChanges = addedTabs.length > 0 || removedTabs.length > 0 || changedTabs.length > 0;

  return {
    hasChanges,
    addedTabs,
    removedTabs,
    changedTabs,
    unchangedTabs,
  };
}

/**
 * 比较两个标签页数组，返回是否有实质性变化
 */
export function compareTabArrays(oldTabs: TabInfo[], newTabs: TabInfo[]): boolean {
  const oldMap = createTabStateMap(oldTabs);
  const newMap = createTabStateMap(newTabs);
  
  const comparison = compareTabStateMaps(oldMap, newMap);
  return comparison.hasChanges;
}

/**
 * 详细的标签页状态比较，返回变化详情
 */
export function getTabStateChanges(
  oldTabs: TabInfo[], 
  newTabs: TabInfo[]
): {
  hasChanges: boolean;
  summary: string;
  details: {
    addedCount: number;
    removedCount: number;
    changedCount: number;
    unchangedCount: number;
  };
  changes: {
    addedTabs: TabInfo[];
    removedTabs: TabInfo[];
    changedTabs: { old: TabInfo; new: TabInfo }[];
  };
} {
  const oldMap = createTabStateMap(oldTabs);
  const newMap = createTabStateMap(newTabs);
  
  const comparison = compareTabStateMaps(oldMap, newMap);
  
  const details = {
    addedCount: comparison.addedTabs.length,
    removedCount: comparison.removedTabs.length,
    changedCount: comparison.changedTabs.length,
    unchangedCount: comparison.unchangedTabs.length,
  };

  let summary = '';
  if (comparison.hasChanges) {
    const parts = [];
    if (details.addedCount > 0) parts.push(`新增${details.addedCount}个`);
    if (details.removedCount > 0) parts.push(`移除${details.removedCount}个`);
    if (details.changedCount > 0) parts.push(`变化${details.changedCount}个`);
    summary = `标签页状态变化: ${parts.join(', ')}`;
  } else {
    summary = '标签页状态无变化';
  }

  return {
    hasChanges: comparison.hasChanges,
    summary,
    details,
    changes: {
      addedTabs: comparison.addedTabs,
      removedTabs: comparison.removedTabs,
      changedTabs: comparison.changedTabs,
    },
  };
}

/**
 * 创建标签页状态快照，用于后续比较
 */
export function createTabStateSnapshot(tabs: TabInfo[]): {
  timestamp: number;
  tabMap: Map<number, TabInfo>;
  tabCount: number;
  openCount: number;
  coreCount: number;
  sessionCount: number;
} {
  const tabMap = createTabStateMap(tabs);
  
  let openCount = 0;
  let coreCount = 0;
  let sessionCount = 0;

  for (const tab of tabs) {
    if (tab.isOpen) openCount++;
    if (tab.isWorkspaceCore) {
      coreCount++;
    } else {
      sessionCount++;
    }
  }

  return {
    timestamp: Date.now(),
    tabMap,
    tabCount: tabs.length,
    openCount,
    coreCount,
    sessionCount,
  };
}

/**
 * 比较两个标签页快照
 */
export function compareTabSnapshots(
  oldSnapshot: ReturnType<typeof createTabStateSnapshot>,
  newSnapshot: ReturnType<typeof createTabStateSnapshot>
): {
  hasChanges: boolean;
  summary: string;
  countChanges: {
    tabCount: number;
    openCount: number;
    coreCount: number;
    sessionCount: number;
  };
} {
  const comparison = compareTabStateMaps(oldSnapshot.tabMap, newSnapshot.tabMap);
  
  const countChanges = {
    tabCount: newSnapshot.tabCount - oldSnapshot.tabCount,
    openCount: newSnapshot.openCount - oldSnapshot.openCount,
    coreCount: newSnapshot.coreCount - oldSnapshot.coreCount,
    sessionCount: newSnapshot.sessionCount - oldSnapshot.sessionCount,
  };

  const hasCountChanges = Object.values(countChanges).some(change => change !== 0);
  const hasChanges = comparison.hasChanges || hasCountChanges;

  let summary = '';
  if (hasChanges) {
    const parts = [];
    if (countChanges.tabCount !== 0) parts.push(`总数${countChanges.tabCount > 0 ? '+' : ''}${countChanges.tabCount}`);
    if (countChanges.openCount !== 0) parts.push(`打开${countChanges.openCount > 0 ? '+' : ''}${countChanges.openCount}`);
    if (countChanges.coreCount !== 0) parts.push(`核心${countChanges.coreCount > 0 ? '+' : ''}${countChanges.coreCount}`);
    if (countChanges.sessionCount !== 0) parts.push(`会话${countChanges.sessionCount > 0 ? '+' : ''}${countChanges.sessionCount}`);
    summary = `快照比较: ${parts.join(', ')}`;
  } else {
    summary = '快照无变化';
  }

  return {
    hasChanges,
    summary,
    countChanges,
  };
}

console.log('🔍 [TabStateComparator] 标签页状态比较工具已加载');
