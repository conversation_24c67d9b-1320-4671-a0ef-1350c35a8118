import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { WorkSpace, Website } from '@/types/workspace';
import WorkspaceItem from './WorkspaceItem';

interface SortableWorkspaceItemProps {
  workspace: WorkSpace;
  isActive: boolean;
  isExpanded: boolean;
  onWorkspaceClick: () => void;
  onToggleExpand: () => void;
  onUpdateWorkspace: (updates: { name?: string; icon?: string; color?: string }) => void;
  onDeleteWorkspace: () => void;
  onAddCurrentTab: () => void;
  onAddWebsiteUrl: (url: string) => void;
  onRemoveWebsite: (websiteId: string) => void;
  onUpdateWebsite: (websiteId: string, updates: { url?: string; title?: string; isPinned?: boolean }) => void;
  onReorderWebsites: (websiteIds: string[]) => void;
  onTogglePin?: (websiteId: string, isPinned: boolean) => void;
  onBatchPin?: (websiteIds: string[], isPinned: boolean) => void;
  onBatchDelete?: (websiteIds: string[]) => void;
  isDragging?: boolean;
}

// 扩展WorkspaceItem的props以支持拖拽
interface ExtendedWorkspaceItemProps extends SortableWorkspaceItemProps {
  dragHandleRef?: (element: HTMLElement | null) => void;
  dragAttributes?: any;
  dragListeners?: any;
}

/**
 * 可拖拽的工作区项目组件
 */
const SortableWorkspaceItem: React.FC<SortableWorkspaceItemProps> = ({
  workspace,
  isDragging = false,
  ...props
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
    setActivatorNodeRef,
  } = useSortable({
    id: workspace.id,
    data: {
      type: 'workspace',
      workspace,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        ${isDragging || isSortableDragging ? 'opacity-50 scale-95' : ''}
        transition-all duration-200
      `}
    >
      <WorkspaceItem
        workspace={workspace}
        dragHandleRef={setActivatorNodeRef}
        dragAttributes={attributes}
        dragListeners={listeners}
        {...props}
      />
    </div>
  );
};

export default SortableWorkspaceItem;
