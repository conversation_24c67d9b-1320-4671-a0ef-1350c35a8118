import React, { useState, useEffect } from 'react';
import {
  ChevronDown,
  ChevronRight,
  MoreVertical,
  Edit,
  Trash2,
  Plus,
  Link,
  Monitor,
  CheckSquare,
  Eye,
  EyeOff,
  Loader2,
  User
} from 'lucide-react';
import {
  DndContext,
  DragOverlay,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragStartEvent,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { WorkSpace, Website, WorkspaceTab } from '@/types/workspace';
import WebsiteList from './WebsiteList';
import TabItem, { TabInfo } from './TabItem';
import DropdownMenu from './DropdownMenu';
import EditWorkspaceModal from './EditWorkspaceModal';
import AddWebsiteModal from './AddWebsiteModal';
import EditWebsiteModal from './EditWebsiteModal';
import ConfirmDialog from './ConfirmDialog';

interface WorkspaceItemProps {
  workspace: WorkSpace;
  isActive: boolean;
  isExpanded: boolean;
  onWorkspaceClick: () => void;
  onToggleExpand: () => void;
  onUpdateWorkspace: (updates: { name?: string; icon?: string; color?: string }) => void;
  onDeleteWorkspace: () => void;
  onAddCurrentTab: () => void;
  onAddWebsiteUrl: (url: string) => void;
  onRemoveWebsite: (websiteId: string) => void;
  onUpdateWebsite: (websiteId: string, updates: { url?: string; title?: string; isPinned?: boolean }) => void;
  onReorderWebsites: (websiteIds: string[]) => void;
  onTogglePin?: (websiteId: string, isPinned: boolean) => void;
  onBatchPin?: (websiteIds: string[], isPinned: boolean) => void;
  onBatchDelete?: (websiteIds: string[]) => void;
}

// 用户标签页状态接口
interface UserTabsState {
  isHidden: boolean;
  hiddenTabsCount: number;
  totalUserTabs: number;
  visibleUserTabs: number;
  canContinueHiding: boolean;
  actionType: 'hide' | 'continue_hide' | 'show';
  loading: boolean;
}

// 工作区标签页状态接口
interface WorkspaceTabsState {
  coreWorkspaceTabs: TabInfo[];
  sessionTabs: TabInfo[];
  totalCount: number;
  openCount: number;
  loading: boolean;
}

/**
 * 工作区项目组件
 */
const WorkspaceItem: React.FC<WorkspaceItemProps> = ({
  workspace,
  isActive,
  isExpanded,
  onWorkspaceClick,
  onToggleExpand,
  onUpdateWorkspace,
  onDeleteWorkspace,
  onAddCurrentTab,
  onAddWebsiteUrl,
  onRemoveWebsite,
  onUpdateWebsite,
  onReorderWebsites,
  // Workona 风格：移除固定标签页相关参数
  onBatchDelete,
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditWebsiteModal, setShowEditWebsiteModal] = useState(false);
  const [editingWebsite, setEditingWebsite] = useState<Website | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [batchMode, setBatchMode] = useState(false);

  // 用户标签页状态
  const [userTabsState, setUserTabsState] = useState<UserTabsState>({
    isHidden: false,
    hiddenTabsCount: 0,
    totalUserTabs: 0,
    visibleUserTabs: 0,
    canContinueHiding: false,
    actionType: 'hide',
    loading: false,
  });

  // 工作区标签页状态
  const [workspaceTabsState, setWorkspaceTabsState] = useState<WorkspaceTabsState>({
    coreWorkspaceTabs: [],
    sessionTabs: [],
    totalCount: 0,
    openCount: 0,
    loading: false,
  });

  // 拖拽状态
  const [activeTab, setActiveTab] = useState<TabInfo | null>(null);

  // 拖拽传感器配置
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor)
  );
  /**
   * 加载工作区用户标签页状态
   */
  const loadUserTabsState = async () => {
    try {
      setUserTabsState(prev => ({ ...prev, loading: true }));

      // 动态导入工作区用户标签页管理器
      const { WorkspaceUserTabsVisibilityManager } = await import('@/utils/tabs');
      const result = await WorkspaceUserTabsVisibilityManager.getWorkspaceUserTabsState(workspace.id);

      if (result.success) {
        const data = result.data!;
        setUserTabsState({
          isHidden: data.isHidden,
          hiddenTabsCount: data.hiddenTabIds.length,
          totalUserTabs: data.totalUserTabs,
          visibleUserTabs: data.visibleUserTabs,
          canContinueHiding: data.canContinueHiding,
          actionType: data.actionType,
          loading: false,
        });
      } else {
        console.error('获取工作区用户标签页状态失败:', result.error);
        setUserTabsState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('加载工作区用户标签页状态时出错:', error);
      setUserTabsState(prev => ({ ...prev, loading: false }));
    }
  };

  /**
   * 切换工作区用户标签页显示状态
   */
  const toggleUserTabsVisibility = async () => {
    try {
      setUserTabsState(prev => ({ ...prev, loading: true }));

      const { WorkspaceUserTabsVisibilityManager } = await import('@/utils/tabs');
      const result = await WorkspaceUserTabsVisibilityManager.toggleWorkspaceUserTabsVisibility(workspace);

      if (result.success) {
        // 重新加载状态
        await loadUserTabsState();
      } else {
        console.error('切换工作区用户标签页状态失败:', result.error);
        setUserTabsState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('切换工作区用户标签页状态时出错:', error);
      setUserTabsState(prev => ({ ...prev, loading: false }));
    }
  };

  /**
   * 加载工作区标签页列表
   */
  const loadWorkspaceTabs = async () => {
    try {
      setWorkspaceTabsState(prev => ({ ...prev, loading: true }));

      // 动态导入 WorkonaTabManager
      const { WorkonaTabManager } = await import('@/utils/workonaTabManager');
      const result = await WorkonaTabManager.getWorkspaceTabsWithStates(workspace.id);

      if (result.success) {
        const data = result.data!;

        // 转换为 TabInfo 格式
        const coreTabInfos: TabInfo[] = data.coreWorkspaceTabs.map(tab => ({
          id: tab.id,
          chromeTabId: tab.chromeTabId,
          title: tab.title,
          url: tab.url,
          favicon: tab.favicon,
          isOpen: tab.isOpen,
          isActive: tab.isActive,
          isPinned: tab.isPinned,
          isWorkspaceCore: true,
          windowId: tab.windowId,
          index: tab.index,
        }));

        const sessionTabInfos: TabInfo[] = data.sessionTabs.map(tab => ({
          id: tab.id,
          chromeTabId: tab.chromeTabId,
          title: tab.title,
          url: tab.url,
          favicon: tab.favicon,
          isOpen: tab.isOpen,
          isActive: tab.isActive,
          isPinned: tab.isPinned,
          isWorkspaceCore: false,
          windowId: tab.windowId,
          index: tab.index,
        }));

        setWorkspaceTabsState({
          coreWorkspaceTabs: coreTabInfos,
          sessionTabs: sessionTabInfos,
          totalCount: data.totalCount,
          openCount: data.openCount,
          loading: false,
        });

        console.log(`📊 工作区标签页加载完成: 核心${coreTabInfos.length}个, 会话${sessionTabInfos.length}个`);
      } else {
        console.error('加载工作区标签页失败:', result.error);
        setWorkspaceTabsState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('加载工作区标签页时出错:', error);
      setWorkspaceTabsState(prev => ({ ...prev, loading: false }));
    }
  };

  /**
   * 激活标签页
   */
  const handleActivateTab = async (chromeTabId: number) => {
    try {
      await chrome.tabs.update(chromeTabId, { active: true });
      console.log(`🎯 激活标签页: ${chromeTabId}`);
    } catch (error) {
      console.error('激活标签页失败:', error);
    }
  };

  /**
   * 转换标签页类型
   */
  const handleConvertTabType = async (workonaId: string, toWorkspaceCore: boolean) => {
    try {
      console.log(`🔄 转换标签页类型: ${workonaId} -> ${toWorkspaceCore ? '核心' : '会话'}`);

      const { WorkonaTabManager } = await import('@/utils/workonaTabManager');
      const result = await WorkonaTabManager.convertTabType(workonaId, toWorkspaceCore);

      if (result.success) {
        console.log(`✅ 标签页类型转换成功`);
        // 重新加载标签页列表
        await loadWorkspaceTabs();
      } else {
        console.error('标签页类型转换失败:', result.error);
      }
    } catch (error) {
      console.error('转换标签页类型时出错:', error);
    }
  };

  /**
   * 拖拽开始事件处理
   */
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const draggedTab = [...workspaceTabsState.coreWorkspaceTabs, ...workspaceTabsState.sessionTabs]
      .find(tab => tab.id === active.id);

    if (draggedTab) {
      setActiveTab(draggedTab);
      console.log(`🎯 开始拖拽标签页: ${draggedTab.title}`);
    }
  };

  /**
   * 拖拽结束事件处理
   */
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveTab(null);

    if (!over || active.id === over.id) {
      return; // 没有有效的放置目标或拖拽到自己
    }

    try {
      // 查找被拖拽的标签页
      const draggedTab = [...workspaceTabsState.coreWorkspaceTabs, ...workspaceTabsState.sessionTabs]
        .find(tab => tab.id === active.id);

      if (!draggedTab) {
        console.warn('找不到被拖拽的标签页');
        return;
      }

      // 判断目标分组
      const targetIsCore = over.data.current?.sortable?.containerId === 'core-tabs';
      const targetIsSession = over.data.current?.sortable?.containerId === 'session-tabs';

      if (!targetIsCore && !targetIsSession) {
        console.warn('无效的拖拽目标');
        return;
      }

      const toWorkspaceCore = targetIsCore;

      // 检查是否需要转换类型
      if (draggedTab.isWorkspaceCore === toWorkspaceCore) {
        console.log('标签页已在目标分组中，无需转换');
        return;
      }

      console.log(`🔄 拖拽转换标签页类型: ${draggedTab.title} -> ${toWorkspaceCore ? '核心' : '会话'}`);

      // 执行类型转换
      await handleConvertTabType(draggedTab.id, toWorkspaceCore);
    } catch (error) {
      console.error('拖拽转换标签页类型时出错:', error);
    }
  };

  // 当工作区变为活跃时加载用户标签页状态和工作区标签页
  useEffect(() => {
    if (isActive) {
      loadUserTabsState();
      loadWorkspaceTabs();
    }
  }, [isActive, workspace.id]);

  // 实时监控标签页状态（1秒间隔）
  useEffect(() => {
    if (!isActive || !isExpanded) return;

    const interval = setInterval(() => {
      loadWorkspaceTabs();
    }, 1000);

    return () => clearInterval(interval);
  }, [isActive, isExpanded, workspace.id]);

  // 监听工作区状态变化（实时监控集成）
  useEffect(() => {
    const setupStateListener = async () => {
      const handleStateUpdate = (workspaceId: string, eventType: 'switch' | 'userTabsVisibility') => {
        if (workspaceId === workspace.id) {
          console.log(`📊 收到工作区 ${workspace.name} 的实时状态更新: ${eventType}`);

          if (eventType === 'userTabsVisibility') {
            // 实时更新用户标签页状态
            loadUserTabsState();
          } else if (eventType === 'switch') {
            // 工作区切换完成，立即更新用户标签页状态
            console.log(`🔄 工作区 ${workspace.name} 切换完成，更新用户标签页状态`);
            loadUserTabsState();
          }
        }
      };

      const { WorkspaceStateSync } = await import('@/utils/workspaceStateSync');
      const removeListener = WorkspaceStateSync.addStateListener(handleStateUpdate);

      return removeListener;
    };

    let removeListener: (() => void) | null = null;

    setupStateListener().then(cleanup => {
      removeListener = cleanup;
    }).catch(error => {
      console.warn('设置工作区状态监听器失败:', error);
    });

    return () => {
      if (removeListener) {
        removeListener();
      }
    };
  }, [workspace.id, loadUserTabsState]);

  /**
   * 处理批量固定
   */
  const handleBatchPin = async (websiteIds: string[]) => {
    try {
      console.log(`📌 批量固定网站: ${websiteIds.length} 个`);

      for (const websiteId of websiteIds) {
        // 更新网站的固定状态
        onUpdateWebsite(websiteId, { isPinned: true });

        // 查找对应的标签页并固定
        const website = workspace.websites.find(w => w.id === websiteId);
        if (website) {
          await pinWebsiteTabs(website);
        }
      }

      console.log(`✅ 批量固定完成: ${websiteIds.length} 个网站`);
    } catch (error) {
      console.error('❌ 批量固定失败:', error);
    }
  };

  /**
   * 处理批量取消固定
   */
  const handleBatchUnpin = async (websiteIds: string[]) => {
    try {
      console.log(`📌 批量取消固定网站: ${websiteIds.length} 个`);

      for (const websiteId of websiteIds) {
        // 更新网站的固定状态
        onUpdateWebsite(websiteId, { isPinned: false });

        // 查找对应的标签页并取消固定
        const website = workspace.websites.find(w => w.id === websiteId);
        if (website) {
          await unpinWebsiteTabs(website);
        }
      }

      console.log(`✅ 批量取消固定完成: ${websiteIds.length} 个网站`);
    } catch (error) {
      console.error('❌ 批量取消固定失败:', error);
    }
  };

  /**
   * 固定网站对应的标签页（使用Workona ID映射）
   */
  const pinWebsiteTabs = async (website: Website) => {
    try {
      // 通过Workona ID映射查找对应的标签页
      const { WorkonaTabManager } = await import('@/utils/workonaTabManager');
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspace.id);

      if (!workonaTabIds.success) {
        console.error('❌ 获取工作区Workona标签页ID失败:', workonaTabIds.error);
        return;
      }

      for (const workonaId of workonaTabIds.data!) {
        try {
          // 检查是否是对应网站的标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.websiteId === website.id) {
            // 获取Chrome标签页ID
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              const tab = await chrome.tabs.get(chromeIdResult.data);
              if (tab && !tab.pinned) {
                await chrome.tabs.update(tab.id!, { pinned: true });
                console.log(`📌 固定标签页: ${tab.title}`);
              }
            }
          }
        } catch (error) {
          console.warn(`⚠️ 处理Workona标签页 ${workonaId} 时出错:`, error);
        }
      }
    } catch (error) {
      console.error('❌ 固定标签页失败:', error);
    }
  };

  /**
   * 取消固定网站对应的标签页（使用Workona ID映射）
   */
  const unpinWebsiteTabs = async (website: Website) => {
    try {
      // 通过Workona ID映射查找对应的标签页
      const { WorkonaTabManager } = await import('@/utils/workonaTabManager');
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspace.id);

      if (!workonaTabIds.success) {
        console.error('❌ 获取工作区Workona标签页ID失败:', workonaTabIds.error);
        return;
      }

      for (const workonaId of workonaTabIds.data!) {
        try {
          // 检查是否是对应网站的标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.websiteId === website.id) {
            // 获取Chrome标签页ID
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              const tab = await chrome.tabs.get(chromeIdResult.data);
              if (tab && tab.pinned) {
                await chrome.tabs.update(tab.id!, { pinned: false });
                console.log(`📌 取消固定标签页: ${tab.title}`);
              }
            }
          }
        } catch (error) {
          console.warn(`⚠️ 处理Workona标签页 ${workonaId} 时出错:`, error);
        }
      }
    } catch (error) {
      console.error('❌ 取消固定标签页失败:', error);
    }
  };








  /**
   * 处理菜单项点击
   */
  const handleMenuClick = (action: string) => {
    setShowDropdown(false);

    switch (action) {
      case 'edit':
        setShowEditModal(true);
        break;
      case 'delete':
        setShowDeleteConfirm(true);
        break;
      case 'add-url':
        setShowAddModal(true);
        break;
      case 'batch-operations':
        setBatchMode(!batchMode);
        // 进入批量模式时自动展开工作区
        if (!batchMode && !isExpanded) {
          onToggleExpand();
        }
        break;
    }
  };

  /**
   * 处理编辑工作区
   */
  const handleEditWorkspace = (updates: { name?: string; icon?: string; color?: string }) => {
    onUpdateWorkspace(updates);
    setShowEditModal(false);
  };

  /**
   * 处理删除确认
   */
  const handleDeleteConfirm = () => {
    onDeleteWorkspace();
    setShowDeleteConfirm(false);
  };

  /**
   * 处理添加网站URL
   */
  const handleAddWebsite = (url: string) => {
    onAddWebsiteUrl(url);
    setShowAddModal(false);
  };

  /**
   * 处理编辑网站
   */
  const handleEditWebsite = (website: Website) => {
    setEditingWebsite(website);
    setShowEditWebsiteModal(true);
  };

  /**
   * 处理保存网站编辑
   */
  const handleSaveWebsiteEdit = (updates: { url?: string; title?: string; isPinned?: boolean }) => {
    if (editingWebsite) {
      onUpdateWebsite(editingWebsite.id, updates);
      setShowEditWebsiteModal(false);
      setEditingWebsite(null);
    }
  };

  const menuItems = [
    {
      id: 'add-url',
      label: '添加网站URL',
      icon: Link,
    },
    ...(workspace.websites.length > 0 ? [{
      id: 'batch-operations',
      label: '批量操作',
      icon: CheckSquare,
    }] : []),
    {
      id: 'edit',
      label: '编辑工作区',
      icon: Edit,
    },
    {
      id: 'delete',
      label: '删除工作区',
      icon: Trash2,
      className: 'text-red-400 hover:text-red-300',
    },
  ];

  return (
    <div className={`workspace-item ${isActive ? 'active' : ''}`}>
      {/* 工作区头部 - 左对齐充满布局 */}
      <div className="flex items-center w-full">
        <div
          className="flex items-center gap-2 flex-1 min-w-0 cursor-pointer"
          onClick={onWorkspaceClick}
        >
          {/* 展开/折叠按钮 */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleExpand();
            }}
            className="p-1 hover:bg-slate-600 rounded transition-colors duration-150 flex-shrink-0"
          >
            {isExpanded ? (
              <ChevronDown className="w-3.5 h-3.5 text-slate-400" />
            ) : (
              <ChevronRight className="w-3.5 h-3.5 text-slate-400" />
            )}
          </button>

          {/* 工作区图标 */}
          <div
            className="w-7 h-7 rounded flex items-center justify-center text-base flex-shrink-0"
            style={{ backgroundColor: workspace.color + '20', color: workspace.color }}
          >
            {workspace.icon}
          </div>

          {/* 工作区信息 - 充满剩余空间 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <h3 className="font-medium text-white truncate text-sm">
                {workspace.name}
              </h3>
              {isActive && (
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse flex-shrink-0" />
              )}
              {/* Workona 风格：显示工作区类型 */}
              {workspace.type && workspace.type !== 'saved' && (
                <span className={`text-xs px-1.5 py-0.5 rounded text-white flex-shrink-0 ${
                  workspace.type === 'temp' ? 'bg-orange-600' : 'bg-blue-600'
                }`}>
                  {workspace.type === 'temp' ? '临时' : '未保存'}
                </span>
              )}
            </div>
            <div className="flex items-center gap-2 text-xs text-slate-400">
              <span>{workspace.websites.length} 个网站</span>
            </div>
          </div>
        </div>

        {/* 操作按钮组 */}
        <div className="flex items-center gap-1">


          {/* 更多操作按钮 */}
          {/* 添加当前标签页按钮 */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onAddCurrentTab();
            }}
            className="p-2 hover:bg-slate-600 rounded transition-colors duration-150"
            title="添加当前标签页"
          >
            <Monitor className="w-4 h-4 text-slate-400 hover:text-green-400" />
          </button>

          {/* 用户标签页隐藏/显示按钮 */}
          {isActive && (
            <div className="relative">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleUserTabsVisibility();
                }}
                disabled={userTabsState.loading || userTabsState.totalUserTabs === 0}
                className={`p-2 rounded transition-colors duration-150 ${
                  userTabsState.loading || userTabsState.totalUserTabs === 0
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:bg-slate-600'
                }`}
              title={
                userTabsState.totalUserTabs === 0
                  ? '没有用户标签页'
                  : userTabsState.actionType === 'show'
                  ? `显示 ${userTabsState.hiddenTabsCount} 个隐藏的用户标签页`
                  : userTabsState.actionType === 'continue_hide'
                  ? `继续隐藏 ${userTabsState.visibleUserTabs} 个可见的用户标签页`
                  : `隐藏 ${userTabsState.visibleUserTabs} 个用户标签页`
              }
            >
              {userTabsState.loading ? (
                <Loader2 className="w-4 h-4 text-slate-400 animate-spin" />
              ) : userTabsState.actionType === 'show' ? (
                <Eye className="w-4 h-4 text-slate-400 hover:text-blue-400" />
              ) : (
                <EyeOff className="w-4 h-4 text-slate-400 hover:text-orange-400" />
              )}
                {/* 显示用户标签页计数 */}
                {!userTabsState.loading && userTabsState.totalUserTabs > 0 && (
                  <span className="absolute -top-1 -right-1 bg-blue-600 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                    {userTabsState.totalUserTabs}
                  </span>
                )}
              </button>
            </div>
          )}

          {/* 更多菜单按钮 */}
          <div className="relative">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowDropdown(!showDropdown);
            }}
            className="p-2 hover:bg-slate-600 rounded transition-colors duration-150"
          >
            <MoreVertical className="w-4 h-4 text-slate-400" />
          </button>

          {/* 下拉菜单 */}
          {showDropdown && (
            <DropdownMenu
              items={menuItems}
              onItemClick={handleMenuClick}
              onClose={() => setShowDropdown(false)}
            />
          )}
          </div>
        </div>
      </div>

      {/* 网站列表 - 左对齐充满 */}
      {isExpanded && workspace.websites.length > 0 && (
        <div className="mt-2">
          <WebsiteList
            websites={workspace.websites}
            onRemoveWebsite={onRemoveWebsite}
            onEditWebsite={handleEditWebsite}
            onReorderWebsites={onReorderWebsites}
            onBatchDelete={onBatchDelete}
            onBatchPin={handleBatchPin}
            onBatchUnpin={handleBatchUnpin}
            batchMode={batchMode}
            onExitBatchMode={() => setBatchMode(false)}
          />
        </div>
      )}

      {/* 标签页列表 - 只在活跃工作区展开时显示 */}
      {isExpanded && isActive && (workspaceTabsState.totalCount > 0 || workspaceTabsState.loading) && (
        <div className="mt-3 space-y-3">
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          >
            {/* 工作区核心标签页分组 */}
            {(workspaceTabsState.coreWorkspaceTabs.length > 0 || workspaceTabsState.loading) && (
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Monitor className="w-3.5 h-3.5 text-blue-400" />
                  <h4 className="text-xs font-medium text-blue-400">
                    工作区核心标签页
                  </h4>
                  <span className="text-xs text-slate-500">
                    ({workspaceTabsState.loading ? '...' : workspaceTabsState.coreWorkspaceTabs.length})
                  </span>
                </div>
                {workspaceTabsState.loading ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="w-4 h-4 text-slate-400 animate-spin" />
                    <span className="ml-2 text-xs text-slate-400">加载中...</span>
                  </div>
                ) : (
                  <SortableContext
                    items={workspaceTabsState.coreWorkspaceTabs.map(tab => tab.id)}
                    strategy={verticalListSortingStrategy}
                    id="core-tabs"
                  >
                    <div className="space-y-1 min-h-[2rem] p-2 border-2 border-dashed border-transparent hover:border-blue-500/30 transition-colors duration-200">
                      {workspaceTabsState.coreWorkspaceTabs.map((tab) => (
                        <TabItem
                          key={tab.id}
                          tab={tab}
                          onActivateTab={handleActivateTab}
                          onConvertType={handleConvertTabType}
                          isDragging={activeTab?.id === tab.id}
                        />
                      ))}
                      {workspaceTabsState.coreWorkspaceTabs.length === 0 && (
                        <div className="text-xs text-slate-500 text-center py-2">
                          拖拽会话标签页到此处转为核心标签页
                        </div>
                      )}
                    </div>
                  </SortableContext>
                )}
              </div>
            )}

            {/* 用户会话标签页分组 */}
            {(workspaceTabsState.sessionTabs.length > 0 || workspaceTabsState.loading) && (
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <User className="w-3.5 h-3.5 text-orange-400" />
                  <h4 className="text-xs font-medium text-orange-400">
                    用户会话标签页
                  </h4>
                  <span className="text-xs text-slate-500">
                    ({workspaceTabsState.loading ? '...' : workspaceTabsState.sessionTabs.length})
                  </span>
                </div>
                {workspaceTabsState.loading ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="w-4 h-4 text-slate-400 animate-spin" />
                    <span className="ml-2 text-xs text-slate-400">加载中...</span>
                  </div>
                ) : (
                  <SortableContext
                    items={workspaceTabsState.sessionTabs.map(tab => tab.id)}
                    strategy={verticalListSortingStrategy}
                    id="session-tabs"
                  >
                    <div className="space-y-1 min-h-[2rem] p-2 border-2 border-dashed border-transparent hover:border-orange-500/30 transition-colors duration-200">
                      {workspaceTabsState.sessionTabs.map((tab) => (
                        <TabItem
                          key={tab.id}
                          tab={tab}
                          onActivateTab={handleActivateTab}
                          onConvertType={handleConvertTabType}
                          isDragging={activeTab?.id === tab.id}
                        />
                      ))}
                      {workspaceTabsState.sessionTabs.length === 0 && (
                        <div className="text-xs text-slate-500 text-center py-2">
                          拖拽核心标签页到此处转为会话标签页
                        </div>
                      )}
                    </div>
                  </SortableContext>
                )}
              </div>
            )}

            {/* 拖拽预览 */}
            <DragOverlay>
              {activeTab ? (
                <TabItem
                  tab={activeTab}
                  onActivateTab={() => {}}
                  onConvertType={() => {}}
                  isDragging={true}
                  className="rotate-3 shadow-lg"
                />
              ) : null}
            </DragOverlay>
          </DndContext>
        </div>
      )}

      {/* 空状态 - 充满空间 */}
      {isExpanded && workspace.websites.length === 0 && (
        <div className="mt-2 p-3 border-2 border-dashed border-slate-600 rounded text-center">
          <Plus className="w-5 h-5 text-slate-500 mx-auto mb-2" />
          <p className="text-xs text-slate-400 mb-2">
            还没有添加任何网站
          </p>
          <div className="flex gap-1 justify-center">
            <button
              onClick={onAddCurrentTab}
              className="flex items-center justify-center w-7 h-7 hover:bg-slate-700 rounded transition-colors duration-200"
              title="添加当前标签页"
            >
              <Monitor className="w-3.5 h-3.5 text-slate-400" />
            </button>
            <button
              onClick={() => setShowAddModal(true)}
              className="flex items-center justify-center w-7 h-7 hover:bg-slate-700 rounded transition-colors duration-200"
              title="添加URL"
            >
              <Link className="w-3.5 h-3.5 text-slate-400" />
            </button>
          </div>
        </div>
      )}

      {/* 编辑工作区模态框 */}
      {showEditModal && (
        <EditWorkspaceModal
          workspace={workspace}
          onClose={() => setShowEditModal(false)}
          onSave={handleEditWorkspace}
        />
      )}

      {/* 添加网站模态框 */}
      {showAddModal && (
        <AddWebsiteModal
          onClose={() => setShowAddModal(false)}
          onAdd={handleAddWebsite}
        />
      )}

      {/* 编辑网站模态框 */}
      {showEditWebsiteModal && editingWebsite && (
        <EditWebsiteModal
          website={editingWebsite}
          onClose={() => {
            setShowEditWebsiteModal(false);
            setEditingWebsite(null);
          }}
          onSave={handleSaveWebsiteEdit}
        />
      )}

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <ConfirmDialog
          title="删除工作区"
          message={`确定要删除工作区"${workspace.name}"吗？此操作无法撤销。`}
          confirmText="删除"
          confirmButtonClass="btn-danger"
          onConfirm={handleDeleteConfirm}
          onCancel={() => setShowDeleteConfirm(false)}
        />
      )}
    </div>
  );
};

export default WorkspaceItem;
