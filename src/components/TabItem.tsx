import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ExternalLink, GripVertical, Monitor, User } from 'lucide-react';

/**
 * 标签页信息接口
 */
export interface TabInfo {
  id: string; // Workona ID (t-{workspaceId}-{uuid})
  chromeTabId: number; // Chrome 原生标签页ID
  title: string;
  url: string;
  favicon?: string;
  isOpen: boolean;
  isActive: boolean;
  isPinned: boolean;
  isWorkspaceCore: boolean; // 是否为工作区核心标签页
  windowId: number;
  index: number;
}

interface TabItemProps {
  tab: TabInfo;
  onActivateTab: (chromeTabId: number) => void;
  onConvertType: (workonaId: string, toWorkspaceCore: boolean) => void;
  isDragging?: boolean;
  className?: string;
}

/**
 * 标签页显示组件
 * 支持工作区核心标签页和用户会话标签页的统一显示
 */
const TabItem: React.FC<TabItemProps> = ({
  tab,
  onActivateTab,
  onConvertType,
  isDragging = false,
  className = '',
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({
    id: tab.id,
    data: {
      type: 'tab',
      tab,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  /**
   * 获取标签页图标
   */
  const getTabIcon = () => {
    if (tab.favicon && tab.favicon !== '') {
      return (
        <img
          src={tab.favicon}
          alt=""
          className="w-4 h-4 rounded"
          onError={(e) => {
            // 如果图标加载失败，显示默认图标
            (e.target as HTMLImageElement).style.display = 'none';
          }}
        />
      );
    }

    // 默认图标
    return (
      <div className="w-4 h-4 bg-slate-600 rounded flex items-center justify-center">
        <ExternalLink className="w-2.5 h-2.5 text-slate-400" />
      </div>
    );
  };

  /**
   * 格式化URL显示
   */
  const formatUrl = (url: string) => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch {
      return url;
    }
  };

  /**
   * 处理标签页点击
   */
  const handleTabClick = () => {
    if (!isDragging && !isSortableDragging) {
      onActivateTab(tab.chromeTabId);
    }
  };

  /**
   * 处理类型转换
   */
  const handleConvertType = (e: React.MouseEvent) => {
    e.stopPropagation();
    onConvertType(tab.id, !tab.isWorkspaceCore);
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        tab-item group relative
        flex items-center gap-2 p-2 rounded
        border border-slate-600/50
        transition-all duration-200
        ${tab.isWorkspaceCore 
          ? 'bg-slate-700/60 hover:bg-slate-700/80 border-blue-500/30' 
          : 'bg-slate-800/60 hover:bg-slate-800/80 border-slate-600/30'
        }
        ${tab.isActive ? 'ring-1 ring-blue-500/50' : ''}
        ${isDragging || isSortableDragging ? 'opacity-50 scale-95' : ''}
        cursor-pointer
        ${className}
      `}
      onClick={handleTabClick}
    >
      {/* 拖拽手柄 */}
      <div
        {...attributes}
        {...listeners}
        className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-grab active:cursor-grabbing"
      >
        <GripVertical className="w-3 h-3 text-slate-400" />
      </div>

      {/* 标签页类型指示器 */}
      <div className="flex-shrink-0">
        {tab.isWorkspaceCore ? (
          <Monitor 
            className="w-3 h-3 text-blue-400" 
            title="工作区核心标签页"
          />
        ) : (
          <User 
            className="w-3 h-3 text-orange-400" 
            title="用户会话标签页"
          />
        )}
      </div>

      {/* 标签页图标 */}
      <div className="flex-shrink-0">
        {getTabIcon()}
      </div>

      {/* 标签页信息 */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <span className="text-sm text-white truncate">
            {tab.title}
          </span>
          {/* 标签页状态指示器 - 与工作区活跃状态指示器完全一致的样式 */}
          {tab.isOpen && (
            <div
              className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse flex-shrink-0"
              title="标签页已打开"
            />
          )}
        </div>
        <p className="text-xs text-slate-400 truncate">
          {formatUrl(tab.url)}
        </p>
      </div>

      {/* 类型转换按钮 */}
      <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <button
          onClick={handleConvertType}
          className={`
            p-1 rounded transition-colors duration-150
            ${tab.isWorkspaceCore 
              ? 'hover:bg-orange-600 text-orange-400 hover:text-white' 
              : 'hover:bg-blue-600 text-blue-400 hover:text-white'
            }
          `}
          title={tab.isWorkspaceCore ? '转为用户标签页' : '转为工作区标签页'}
        >
          {tab.isWorkspaceCore ? (
            <User className="w-3 h-3" />
          ) : (
            <Monitor className="w-3 h-3" />
          )}
        </button>
      </div>
    </div>
  );
};

export default TabItem;
