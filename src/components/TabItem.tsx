import React, { useCallback } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ExternalLink, GripVertical, Monitor, User } from 'lucide-react';
import { TabInfo } from '@/types/workspace';
import { isTabStateEqual } from '@/utils/tabInfoConverter';

interface TabItemProps {
  tab: TabInfo;
  onActivateTab: (chromeTabId: number) => void;
  onConvertType: (workonaId: string, toWorkspaceCore: boolean) => void;
  isDragging?: boolean;
  className?: string;
}

/**
 * 标签页显示组件（React.memo优化版）
 * 支持工作区核心标签页和用户会话标签页的统一显示
 * 使用深度状态比较，只在标签页状态真正变化时重新渲染
 */
const TabItem: React.FC<TabItemProps> = React.memo(({
  tab,
  onActivateTab,
  onConvertType,
  isDragging = false,
  className = '',
}) => {
  // 🚀 性能优化：使用useCallback缓存事件处理函数
  const handleActivateTab = useCallback(() => {
    onActivateTab(tab.chromeTabId || tab.id);
  }, [tab.chromeTabId, tab.id, onActivateTab]);

  const handleConvertType = useCallback(() => {
    const workonaId = tab.workonaId || `t-unknown-${tab.id}`;
    onConvertType(workonaId, !tab.isWorkspaceCore);
  }, [tab.workonaId, tab.id, tab.isWorkspaceCore, onConvertType]);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({
    id: tab.workonaId || `tab-${tab.id}`,
    data: {
      type: 'tab',
      tab,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  /**
   * 获取标签页图标
   */
  const getTabIcon = () => {
    if (tab.favicon && tab.favicon !== '') {
      return (
        <img
          src={tab.favicon}
          alt=""
          className="w-4 h-4 rounded"
          onError={(e) => {
            // 如果图标加载失败，显示默认图标
            (e.target as HTMLImageElement).style.display = 'none';
          }}
        />
      );
    }

    // 默认图标
    return (
      <div className="w-4 h-4 bg-slate-600 rounded flex items-center justify-center">
        <ExternalLink className="w-2.5 h-2.5 text-slate-400" />
      </div>
    );
  };

  /**
   * 格式化URL显示
   */
  const formatUrl = (url: string) => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch {
      return url;
    }
  };

  /**
   * 处理标签页点击（使用缓存的事件处理函数）
   */
  const handleTabClick = useCallback(() => {
    if (!isDragging && !isSortableDragging) {
      handleActivateTab();
    }
  }, [isDragging, isSortableDragging, handleActivateTab]);

  /**
   * 处理类型转换（使用缓存的事件处理函数）
   */
  const handleConvertTypeClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    handleConvertType();
  }, [handleConvertType]);

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        website-item group relative
        ${tab.isActive ? 'bg-blue-600/20 border-blue-500' : ''}
        ${isDragging || isSortableDragging ? 'opacity-50 scale-95' : ''}
        cursor-pointer
        ${className}
      `}
      onClick={handleTabClick}
    >
      {/* 拖拽手柄 */}
      <div
        {...attributes}
        {...listeners}
        className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-grab active:cursor-grabbing"
      >
        <GripVertical className="w-3 h-3 text-slate-400" />
      </div>

      {/* 标签页图标 */}
      <div className="flex-shrink-0">
        {getTabIcon()}
      </div>

      {/* 标签页信息 */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <span className="text-sm text-white truncate">
            {tab.title}
          </span>
          {/* 标签页状态指示器 - 与工作区活跃状态指示器完全一致的样式 */}
          {tab.isOpen && (
            <div
              className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse flex-shrink-0"
              title="标签页已打开"
            />
          )}
        </div>
        <p className="text-xs text-slate-400 truncate">
          {formatUrl(tab.url)}
        </p>
      </div>

      {/* 操作按钮（复用website-actions结构） */}
      <div className="website-actions flex items-center gap-1">
        {/* 标签页类型指示器 */}
        <div className="flex-shrink-0" title={tab.isWorkspaceCore ? "工作区核心标签页" : "用户会话标签页"}>
          {tab.isWorkspaceCore ? (
            <Monitor className="w-3 h-3 text-blue-400" />
          ) : (
            <User className="w-3 h-3 text-orange-400" />
          )}
        </div>

        {/* 类型转换按钮 */}
        <button
          onClick={handleConvertTypeClick}
          className={`
            p-1 rounded transition-colors duration-150
            ${tab.isWorkspaceCore
              ? 'hover:bg-orange-600 text-orange-400 hover:text-white'
              : 'hover:bg-blue-600 text-blue-400 hover:text-white'
            }
          `}
          title={tab.isWorkspaceCore ? '转为用户标签页' : '转为工作区标签页'}
        >
          {tab.isWorkspaceCore ? (
            <User className="w-3 h-3" />
          ) : (
            <Monitor className="w-3 h-3" />
          )}
        </button>
      </div>
    </div>
  );
}, (prevProps: TabItemProps, nextProps: TabItemProps) => {
  // 🔍 自定义比较函数：只有在关键属性变化时才重新渲染
  return (
    isTabStateEqual(prevProps.tab, nextProps.tab) &&
    prevProps.isDragging === nextProps.isDragging &&
    prevProps.className === nextProps.className &&
    prevProps.onActivateTab === nextProps.onActivateTab &&
    prevProps.onConvertType === nextProps.onConvertType
  );
});

// 设置显示名称，便于React DevTools调试
TabItem.displayName = 'TabItem';

export default TabItem;
