import { e as createTabInfoFromChromeTab } from './dataMigration-B4GpAxNA.js';

class TabEventManager {
  static instance = null;
  callbacks = /* @__PURE__ */ new Set();
  isListening = false;
  debounceTimers = /* @__PURE__ */ new Map();
  batchUpdateTimer = null;
  pendingUpdates = /* @__PURE__ */ new Map();
  constructor() {
  }
  /**
   * 获取单例实例
   */
  static getInstance() {
    if (!TabEventManager.instance) {
      TabEventManager.instance = new TabEventManager();
    }
    return TabEventManager.instance;
  }
  /**
   * 添加事件监听回调
   */
  addCallback(callback) {
    this.callbacks.add(callback);
    if (this.callbacks.size === 1 && !this.isListening) {
      this.startListening();
    }
    return () => {
      this.callbacks.delete(callback);
      if (this.callbacks.size === 0 && this.isListening) {
        this.stopListening();
      }
    };
  }
  /**
   * 开始监听Chrome标签页事件
   */
  startListening() {
    if (this.isListening) return;
    console.log("🎧 [TabEventManager] 开始监听Chrome标签页事件");
    chrome.tabs.onCreated.addListener(this.handleTabCreated);
    chrome.tabs.onUpdated.addListener(this.handleTabUpdated);
    chrome.tabs.onRemoved.addListener(this.handleTabRemoved);
    chrome.tabs.onActivated.addListener(this.handleTabActivated);
    chrome.tabs.onMoved.addListener(this.handleTabMoved);
    this.isListening = true;
  }
  /**
   * 停止监听Chrome标签页事件
   */
  stopListening() {
    if (!this.isListening) return;
    console.log("🔇 [TabEventManager] 停止监听Chrome标签页事件");
    chrome.tabs.onCreated.removeListener(this.handleTabCreated);
    chrome.tabs.onUpdated.removeListener(this.handleTabUpdated);
    chrome.tabs.onRemoved.removeListener(this.handleTabRemoved);
    chrome.tabs.onActivated.removeListener(this.handleTabActivated);
    chrome.tabs.onMoved.removeListener(this.handleTabMoved);
    this.debounceTimers.forEach((timer) => clearTimeout(timer));
    this.debounceTimers.clear();
    if (this.batchUpdateTimer) {
      clearTimeout(this.batchUpdateTimer);
      this.batchUpdateTimer = null;
    }
    this.isListening = false;
  }
  /**
   * 处理标签页创建事件
   */
  handleTabCreated = (tab) => {
    console.log(`📝 [TabEventManager] 标签页创建: ${tab.title} (${tab.id})`);
    this.debounceEvent(`created-${tab.id}`, () => {
      this.notifyCallbacks("created", {
        tab: createTabInfoFromChromeTab(tab),
        chromeTab: tab
      });
    }, 100);
  };
  /**
   * 处理标签页更新事件
   */
  handleTabUpdated = (tabId, changeInfo, tab) => {
    const importantChanges = ["status", "title", "url", "pinned", "audible"];
    const hasImportantChange = Object.keys(changeInfo).some((key) => importantChanges.includes(key));
    if (!hasImportantChange) return;
    console.log(`🔄 [TabEventManager] 标签页更新: ${tab.title} (${tabId})`, changeInfo);
    this.pendingUpdates.set(tabId, { changeInfo, tab });
    if (this.batchUpdateTimer) {
      clearTimeout(this.batchUpdateTimer);
    }
    this.batchUpdateTimer = setTimeout(() => {
      this.processBatchUpdates();
    }, 200);
  };
  /**
   * 处理标签页移除事件
   */
  handleTabRemoved = (tabId, removeInfo) => {
    console.log(`🗑️ [TabEventManager] 标签页移除: ${tabId}`);
    this.debounceEvent(`removed-${tabId}`, () => {
      this.notifyCallbacks("removed", {
        tabId,
        removeInfo
      });
    }, 100);
  };
  /**
   * 处理标签页激活事件
   */
  handleTabActivated = (activeInfo) => {
    console.log(`🎯 [TabEventManager] 标签页激活: ${activeInfo.tabId}`);
    this.debounceEvent(`activated-${activeInfo.tabId}`, () => {
      this.notifyCallbacks("activated", {
        tabId: activeInfo.tabId,
        windowId: activeInfo.windowId
      });
    }, 50);
  };
  /**
   * 处理标签页移动事件
   */
  handleTabMoved = (tabId, moveInfo) => {
    console.log(`📍 [TabEventManager] 标签页移动: ${tabId} -> 位置 ${moveInfo.toIndex}`);
    this.debounceEvent(`moved-${tabId}`, () => {
      this.notifyCallbacks("updated", {
        tabId,
        changeInfo: { index: moveInfo.toIndex },
        moveInfo
      });
    }, 100);
  };
  /**
   * 处理批量更新
   */
  processBatchUpdates() {
    if (this.pendingUpdates.size === 0) return;
    console.log(`📦 [TabEventManager] 处理批量更新: ${this.pendingUpdates.size} 个标签页`);
    for (const [tabId, { changeInfo, tab }] of this.pendingUpdates) {
      this.notifyCallbacks("updated", {
        tabId,
        changeInfo,
        tab: createTabInfoFromChromeTab(tab),
        chromeTab: tab
      });
    }
    this.pendingUpdates.clear();
    this.batchUpdateTimer = null;
  }
  /**
   * 防抖事件处理
   */
  debounceEvent(key, callback, delay) {
    const existingTimer = this.debounceTimers.get(key);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }
    const timer = setTimeout(() => {
      callback();
      this.debounceTimers.delete(key);
    }, delay);
    this.debounceTimers.set(key, timer);
  }
  /**
   * 通知所有回调
   */
  notifyCallbacks(eventType, data) {
    for (const callback of this.callbacks) {
      try {
        callback(eventType, data);
      } catch (error) {
        console.error(`❌ [TabEventManager] 回调执行失败:`, error);
      }
    }
  }
  /**
   * 获取当前监听状态
   */
  isCurrentlyListening() {
    return this.isListening;
  }
  /**
   * 获取当前回调数量
   */
  getCallbackCount() {
    return this.callbacks.size;
  }
  /**
   * 清理资源
   */
  cleanup() {
    this.stopListening();
    this.callbacks.clear();
    TabEventManager.instance = null;
  }
}
const tabEventManager = TabEventManager.getInstance();
console.log("🎧 [TabEventManager] 标签页事件管理器已加载");

export { TabEventManager, tabEventManager };
