# 工作区固定状态恢复修复

## 问题描述

在浏览器重启后，原本在工作区1中固定状态的标签页，在切换回工作区1时系统会错误地取消其固定状态。

## 根本原因

1. **Chrome ID 变化**: 浏览器重启后，所有标签页的 Chrome ID 都会发生变化
2. **存储不同步**: 工作区的固定状态存储 (`workspacePinnedTabIds_${workspaceId}`) 仍然保存着旧的 Chrome ID
3. **恢复失败**: 工作区切换时，系统尝试使用过期的 Chrome ID 恢复固定状态，导致恢复失败

## 解决方案

### 1. 增强 TabIdMapping 元数据

在 `TabIdMapping` 接口中添加固定状态相关的元数据：

```typescript
metadata?: {
  // ... 其他字段
  isPinned?: boolean;     // 固定状态
  pinnedAt?: number;      // 固定时间戳
  unpinnedAt?: number;    // 取消固定时间戳
}
```

### 2. 会话存储增强

在标签页的会话存储中保存固定状态信息：

```typescript
const workonaData = {
  workonaId,
  workspaceId,
  websiteId,
  isWorkspaceCore,
  isPinned,  // 新增固定状态
  timestamp: Date.now()
};
```

### 3. 浏览器重启后的恢复逻辑

#### 3.1 更新工作区固定状态存储

```typescript
private async updateWorkspacePinnedTabIds(workspaceId: string, newChromeId: number): Promise<void> {
  // 验证现有的 Chrome ID 是否仍然有效，清理无效的 ID
  // 添加新的 Chrome ID（如果不在列表中）
  // 只有在列表发生变化时才更新存储
}
```

#### 3.2 全面清理固定状态存储

```typescript
private async cleanupAllWorkspacePinnedStates(): Promise<void> {
  // 遍历所有工作区
  // 验证每个 Chrome ID 是否仍然有效且为固定状态
  // 移除无效的条目，更新存储
}
```

### 4. 实时固定状态监听

添加 `chrome.tabs.onUpdated` 监听器来处理固定状态变化：

```typescript
private async handleTabPinnedStateChange(tabId: number, isPinned: boolean, tab: chrome.tabs.Tab): Promise<void> {
  // 更新 TabIdMapping 中的固定状态元数据
  // 更新工作区的固定状态存储
  // 更新标签页的会话存储
}
```

### 5. 移除固定状态的处理

```typescript
private async removeFromWorkspacePinnedTabIds(workspaceId: string, chromeId: number): Promise<void> {
  // 从工作区的固定标签页ID列表中移除指定的 Chrome ID
  // 如果列表为空，删除存储键
}
```

## 修复流程

1. **浏览器重启时**:
   - 通过会话存储恢复 Workona ID 映射
   - 如果标签页原本是固定状态，更新工作区的固定状态存储
   - 清理所有工作区的固定状态存储，移除无效的 Chrome ID

2. **工作区切换时**:
   - 使用更新后的 Chrome ID 恢复固定状态
   - 由于存储已经同步，恢复过程将成功

3. **实时监听**:
   - 监听标签页固定状态变化
   - 实时更新 TabIdMapping 元数据和工作区存储
   - 保持会话存储同步

## 关键改进

1. **数据一致性**: 确保 Chrome ID、TabIdMapping 和工作区存储的一致性
2. **自动清理**: 定期清理无效的 Chrome ID，防止数据污染
3. **实时同步**: 实时监听固定状态变化，保持数据最新
4. **容错处理**: 增加错误处理，确保单个失败不影响整体功能

## 测试验证

1. 在工作区1中固定一些标签页
2. 重启浏览器
3. 切换到工作区1
4. 验证固定状态是否正确恢复

## 文件修改清单

- `src/types/workspace.ts`: 增强 TabIdMapping 接口
- `src/utils/workonaTabManager.ts`: 更新映射创建逻辑
- `src/background/background.ts`: 添加固定状态处理逻辑
- `src/utils/workspaceSwitcher.ts`: 现有的恢复逻辑保持不变

这个修复确保了工作区固定状态在浏览器重启后能够正确恢复，解决了用户体验问题。
