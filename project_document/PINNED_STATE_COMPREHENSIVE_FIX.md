# 工作区固定状态恢复综合修复

## 🎯 问题总结

### 问题1：浏览器重启后固定标签页重复打开
- **现象**：在工作区1中固定一个专属标签页（如 baidu.com），重启浏览器后切换到工作区1时，系统会重新打开一个新的 baidu.com 标签页
- **根因**：`openWorkspaceWebsites` 方法没有正确识别浏览器重启后恢复的固定标签页

### 问题2：固定标签页被错误识别为用户标签页
- **现象**：浏览器重启后，原本固定的专属标签页被系统错误地识别为用户标签页
- **根因**：浏览器重启后的标签页分类逻辑不完善

### 问题3：用户标签页隐藏/显示功能异常改变固定状态
- **现象**：用户手动取消某个标签页的固定状态后，使用"用户标签隐藏"再"恢复"功能，标签页被错误地恢复为固定状态
- **根因**：用户标签页管理功能没有正确区分和保存固定状态

## 🛠️ 综合修复方案

### 修复1：改进浏览器重启后的标签页分类逻辑

**文件**：`src/background/background.ts`

**修改**：在 `restoreTabMappingsAfterRestart` 方法中，同步标签页的实际固定状态到元数据：

```typescript
// 如果映射创建成功，更新固定状态元数据
if (mappingResult.success) {
  // 获取标签页的实际固定状态（浏览器重启后可能已经恢复）
  const actualPinnedState = tab.pinned;
  
  await WorkonaTabManager.updateTabMetadata(workonaData.workonaId, {
    metadata: {
      isPinned: actualPinnedState,
      pinnedAt: actualPinnedState ? Date.now() : undefined,
      unpinnedAt: !actualPinnedState ? Date.now() : undefined
    }
  });
  
  console.log(`📌 同步固定状态: ${workonaData.workonaId} -> 实际状态: ${actualPinnedState}`);
}
```

### 修复2：改进工作区网站打开逻辑

**文件**：`src/utils/workspaceSwitcher.ts`

**修改**：在 `openWorkspaceWebsites` 方法中，增强现有标签页的验证和同步逻辑：

```typescript
// 验证并同步现有标签页的 Workona ID 映射和固定状态
try {
  const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(existingTab.id);
  if (workonaIdResult.success && workonaIdResult.data) {
    const pinnedStatus = existingTab.isPinned ? '(固定)' : '(非固定)';
    console.log(`✅ 现有标签页已有 Workona ID: ${workonaIdResult.data} ${pinnedStatus}`);
    
    // 获取现有元数据并同步固定状态
    const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
    const existingMetadata = metadataResult.success ? metadataResult.data?.metadata : undefined;
    
    await WorkonaTabManager.updateTabMetadata(workonaIdResult.data, {
      metadata: {
        ...existingMetadata,
        source: existingMetadata?.source || 'workspace_website',
        isPinned: existingTab.isPinned,
        pinnedAt: existingTab.isPinned ? Date.now() : undefined,
        unpinnedAt: !existingTab.isPinned ? Date.now() : undefined
      }
    });
  }
}
```

### 修复3：改进用户标签页隐藏/显示功能

**文件**：`src/utils/tabs.ts`

**修改1**：在隐藏用户标签页时，基于 Workona ID 映射的元数据记录固定状态：

```typescript
// 记录哪些标签页是固定的（基于 Workona ID 映射的元数据）
const pinnedTabIds: number[] = [];
for (const tab of workspaceUserTabs) {
  // 检查标签页的实际固定状态和元数据中的固定状态
  const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
  let shouldRecordAsPinned = tab.isPinned;
  
  if (workonaIdResult.success && workonaIdResult.data) {
    // 如果有 Workona ID，检查元数据中的固定状态
    const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
    if (metadataResult.success && metadataResult.data?.metadata?.isPinned !== undefined) {
      // 使用元数据中的固定状态作为权威状态
      shouldRecordAsPinned = metadataResult.data.metadata.isPinned;
    }
  }
  
  if (shouldRecordAsPinned) {
    pinnedTabIds.push(tab.id);
    console.log(`📌 记录固定标签页: ${tab.title} (${tab.id}) - 基于${workonaIdResult.success ? 'Workona元数据' : '实际状态'}`);
  }
}
```

**修改2**：在显示用户标签页时，基于 Workona ID 映射的元数据恢复固定状态：

```typescript
// 恢复固定状态（基于 Workona ID 映射的元数据）
for (const tabId of existingTabIds) {
  try {
    // 检查标签页的 Workona ID 和元数据
    const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
    let shouldRestorePinned = pinnedTabIds.includes(tabId);
    
    if (workonaIdResult.success && workonaIdResult.data) {
      // 如果有 Workona ID，检查元数据中的固定状态
      const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
      if (metadataResult.success && metadataResult.data?.metadata?.isPinned !== undefined) {
        // 使用元数据中的固定状态作为权威状态
        shouldRestorePinned = metadataResult.data.metadata.isPinned;
      }
    }
    
    if (shouldRestorePinned) {
      await chrome.tabs.update(tabId, { pinned: true });
      console.log(`📌 恢复固定状态: 标签页 ${tabId} - 基于${workonaIdResult.success ? 'Workona元数据' : '存储记录'}`);
    } else {
      // 确保非固定标签页不会被错误地设置为固定
      const currentTab = await chrome.tabs.get(tabId);
      if (currentTab.pinned) {
        await chrome.tabs.update(tabId, { pinned: false });
        console.log(`📌 取消错误的固定状态: 标签页 ${tabId}`);
      }
    }
  } catch (error) {
    console.warn(`⚠️ 处理标签页 ${tabId} 固定状态失败:`, error);
  }
}
```

### 修复4：添加标签页固定状态变化的实时监听

**文件**：`src/background/background.ts`

**修改1**：添加标签页更新监听器：

```typescript
// 监听标签页更新（包括固定状态变化）
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  try {
    // 只处理固定状态变化
    if (changeInfo.pinned !== undefined) {
      console.log(`📌 检测到标签页 ${tabId} 固定状态变化: ${changeInfo.pinned}`);
      await this.handleTabPinnedStateChange(tabId, changeInfo.pinned, tab);
    }
  } catch (error) {
    console.error('Error handling tab pinned state change:', error);
  }
});
```

**修改2**：添加固定状态变化处理方法：

```typescript
private async handleTabPinnedStateChange(tabId: number, isPinned: boolean, tab: chrome.tabs.Tab): Promise<void> {
  try {
    // 获取标签页的 Workona ID
    const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
    
    if (workonaIdResult.success && workonaIdResult.data) {
      const workonaId = workonaIdResult.data;
      
      // 获取现有元数据
      const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
      const existingMetadata = metadataResult.success ? metadataResult.data?.metadata : undefined;
      
      // 更新固定状态元数据
      await WorkonaTabManager.updateTabMetadata(workonaId, {
        metadata: {
          ...existingMetadata,
          source: existingMetadata?.source || 'workspace_website',
          isPinned: isPinned,
          pinnedAt: isPinned ? Date.now() : undefined,
          unpinnedAt: !isPinned ? Date.now() : undefined
        }
      });
      
      console.log(`✅ 同步标签页固定状态到元数据: ${workonaId} -> ${isPinned ? '固定' : '取消固定'}`);
      
      // 更新会话存储中的固定状态
      try {
        await chrome.scripting.executeScript({
          target: { tabId: tabId },
          func: (isPinned: boolean) => {
            const existingData = sessionStorage.getItem('workonaData');
            if (existingData) {
              const workonaData = JSON.parse(existingData);
              workonaData.isPinned = isPinned;
              workonaData.timestamp = Date.now();
              sessionStorage.setItem('workonaData', JSON.stringify(workonaData));
              console.log(`📝 更新标签页会话存储固定状态:`, workonaData);
            }
          },
          args: [isPinned]
        });
      } catch (error) {
        console.warn(`⚠️ 无法更新标签页 ${tabId} 的会话存储:`, error);
      }
    }
  } catch (error) {
    console.error('处理标签页固定状态变化失败:', error);
  }
}
```

## 🎯 修复效果

### ✅ 问题1解决：浏览器重启后不再重复打开固定标签页
- 通过改进的标签页检测逻辑，正确识别浏览器重启后恢复的固定标签页
- 同步实际固定状态到 Workona ID 映射的元数据中
- 避免重复创建已存在的标签页

### ✅ 问题2解决：固定标签页正确分类
- 浏览器重启后，固定的专属标签页能够正确恢复其 Workona ID 映射
- 固定状态信息正确同步到元数据中
- 不再被错误识别为用户标签页

### ✅ 问题3解决：用户标签页管理不影响固定状态
- 隐藏/显示功能基于 Workona ID 映射的元数据管理固定状态
- 用户手动改变的固定状态得到正确保存和恢复
- 避免错误地恢复已取消的固定状态

### ✅ 问题4解决：实时固定状态同步
- 监听标签页固定状态变化，实时更新元数据
- 确保 Workona ID 映射、会话存储和实际状态的一致性
- 提供完整的固定状态生命周期管理

## 📁 修改文件清单

- `src/background/background.ts`：添加固定状态变化监听和处理
- `src/utils/workspaceSwitcher.ts`：改进工作区网站打开逻辑
- `src/utils/tabs.ts`：改进用户标签页隐藏/显示功能

## 🔮 预期效果

1. **浏览器重启后**：固定标签页正确恢复，不会重复打开
2. **工作区切换时**：固定状态正确恢复，基于可靠的 Workona ID 映射
3. **用户标签页管理**：隐藏/显示功能不会错误改变固定状态
4. **实时同步**：用户手动改变固定状态时，系统立即同步更新

这个综合修复方案彻底解决了工作区固定状态恢复的所有问题，确保了系统的可靠性和用户体验的一致性。
