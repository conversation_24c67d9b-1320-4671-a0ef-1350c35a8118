# 工作区标签页管理功能

## 功能概述

本次更新为Chrome扩展的工作区管理功能添加了三个重要的增强特性：

### 1. 工作区标签页信息显示
- 在工作区列表界面中显示当前工作区的所有标签页
- 明确区分两种类型的标签页：
  - **工作区核心标签页**：属于工作区配置的核心标签页，用蓝色图标标识
  - **用户会话标签页**：用户临时打开的标签页，用橙色图标标识
- 实时显示标签页状态（打开/关闭）
- 显示标签页的favicon、标题和URL信息

### 2. 标签页类型拖拽转换
- 支持通过拖拽操作改变标签页的归属类型
- 可以将工作区核心标签页拖拽到会话标签页区域，转换为临时标签页
- 可以将用户会话标签页拖拽到核心标签页区域，添加为工作区的核心标签页
- 提供视觉反馈和拖拽预览
- 自动更新Workona ID映射和元数据

### 3. 工作区拖拽排序
- 支持通过拖拽操作调整工作区的显示顺序
- 在工作区头部悬停时显示拖拽手柄
- 提供拖拽预览和视觉反馈
- 自动保存新的工作区排序

### 4. 智能窗口保护机制
- 防止窗口因为没有标签页而被Chrome自动关闭
- 只在特定条件下创建保护标签页：
  - 当前Chrome窗口中只剩下1个用户标签页时
  - 当前Chrome窗口中只剩下1个工作区核心标签页时
- 基于Workona风格标签页分类逻辑（isWorkspaceCore属性）
- 智能识别系统标签页、用户标签页和工作区核心标签页
- 集成到标签页隐藏、工作区切换等操作中

## 技术实现

### 核心组件

#### 1. TabItem 组件 (`src/components/TabItem.tsx`)
- 独立的标签页显示组件
- 支持拖拽功能（基于@dnd-kit）
- 提供类型转换按钮
- 显示标签页状态指示器

#### 2. WorkspaceItem 组件扩展
- 在工作区展开时显示标签页列表
- 集成DndContext支持跨分组拖拽
- 实时监控标签页状态（1秒间隔）
- 分组显示核心标签页和会话标签页

#### 3. WorkonaTabManager 工具方法扩展
- `getWorkspaceAllTabs()`: 获取工作区所有标签页并分类
- `convertTabType()`: 转换标签页类型
- `getWorkspaceTabsWithStates()`: 批量获取标签页状态

#### 4. WindowProtectionManager 窗口保护管理器
- `shouldProtectWindow()`: 检查窗口是否需要保护
- `createProtectionTab()`: 创建保护标签页
- `protectWindowIfNeeded()`: 智能窗口保护
- 集成到WorkspaceUserTabsVisibilityManager和WorkspaceSwitcher中

### 数据结构

#### TabInfo 接口
```typescript
interface TabInfo {
  id: string; // Workona ID (t-{workspaceId}-{uuid})
  chromeTabId: number; // Chrome 原生标签页ID
  title: string;
  url: string;
  favicon?: string;
  isOpen: boolean;
  isActive: boolean;
  isPinned: boolean;
  isWorkspaceCore: boolean; // 是否为工作区核心标签页
  windowId: number;
  index: number;
}
```

### 拖拽功能实现

使用@dnd-kit库实现：
- **DndContext**: 提供拖拽上下文
- **SortableContext**: 为每个标签页分组提供排序上下文
- **DragOverlay**: 显示拖拽预览
- **useSortable**: 在TabItem组件中启用拖拽功能

### 状态管理

#### 实时监控
- 每秒检查标签页状态变化
- 自动更新打开/关闭状态
- 同步Chrome标签页信息

#### 元数据同步
- 拖拽转换时更新`isWorkspaceCore`属性
- 记录转换时间和来源信息
- 维护Workona ID映射的一致性

## 用户界面

### 视觉设计
- **核心标签页**: 蓝色边框和图标，表示工作区的核心内容
- **会话标签页**: 橙色边框和图标，表示临时会话内容
- **状态指示器**: 绿色圆点表示标签页已打开
- **拖拽反馈**: 拖拽时显示半透明预览和目标区域高亮

### 交互设计
- 点击标签页激活对应的Chrome标签页
- 悬停显示拖拽手柄和类型转换按钮
- 拖拽到目标区域自动转换类型
- 空分组显示提示文本引导用户操作

## 性能优化

### 批量操作
- 使用`getWorkspaceTabsWithStates()`批量获取状态
- 避免频繁的单个标签页查询
- 智能缓存和状态更新

### 按需渲染
- 只在工作区展开且活跃时显示标签页列表
- 使用React的条件渲染优化性能
- 实时监控仅在必要时启动

### 错误处理
- 完善的错误捕获和日志记录
- 优雅降级，不影响现有功能
- 用户友好的错误提示

## 兼容性

### 向后兼容
- 不修改现有的工作区管理功能
- 保持现有API接口不变
- 新功能作为增强特性添加

### 数据兼容
- 基于现有的Workona ID映射机制
- 扩展元数据结构，不破坏现有数据
- 支持渐进式功能启用

## 测试

### 单元测试
- WorkonaTabManager方法测试
- 标签页分类逻辑测试
- 类型转换功能测试

### 集成测试
- 拖拽功能端到端测试
- 状态同步测试
- 错误场景测试

## 使用说明

1. **查看标签页**: 展开活跃的工作区即可看到标签页列表
2. **激活标签页**: 点击任意标签页项目激活对应的Chrome标签页
3. **转换类型**: 
   - 方法1: 点击标签页右侧的类型转换按钮
   - 方法2: 拖拽标签页到目标分组区域
4. **实时状态**: 标签页的打开/关闭状态会自动更新

## 未来扩展

- 支持标签页排序
- 添加标签页搜索功能
- 支持批量操作（批量转换、删除等）
- 添加标签页历史记录
- 支持标签页分组管理
